# AI Coach - 多智能体健身教练系统

基于LlamaIndex框架构建的智能健身教练系统，采用多智能体架构，为用户提供个性化的健身指导和服务支持。该系统通过FastAPI提供RESTful API接口，支持多语言交互和个性化健身计划定制。

## 🌟 项目特色

- **多智能体架构**: 采用专业分工的多智能体系统，每个智能体专注特定领域
- **高性能API**: 基于FastAPI构建，支持异步处理和请求限流
- **智能记忆**: 基于Redis/Valkey的用户对话记忆，支持上下文连续对话
- **多语言支持**: 支持中文、英文等多种语言交互
- **个性化定制**: 支持运动类型、时长、器械、姿势等多维度健身计划定制
- **智能路由**: 自动识别用户意图并路由到对应的专业智能体
- **参数关联**: 智能处理运动参数间的关联关系（如椅子瑜伽与坐姿的关联）
- **外部集成**: 支持飞书数据同步、Slack告警等第三方服务集成
- **生产就绪**: 支持多进程部署、定时任务、健康检查等企业级功能

## 📄 项目结构
```
├── .env.example            # 环境变量配置模板
├── .python-version         # Python版本配置
├── README.md              # 项目说明文档
├── app/                   # 应用主目录
│   ├── README.md          # 应用模块说明
│   ├── __init__.py        # 包初始化文件
│   ├── agent/             # 智能体相关模块
│   │   ├── __init__.py    # 智能体包初始化
│   │   ├── agent_chat.py  # 对话处理逻辑
│   │   ├── agent_context.py # 智能体上下文管理
│   │   ├── agent_init.py  # 智能体初始化和配置
│   │   ├── agent_llms.py  # LLM模型配置
│   │   ├── agent_memory.py # 记忆管理模块
│   │   ├── agent_tools.py # 智能体工具集
│   │   └── schema.py      # 智能体数据模型
│   ├── api/               # API接口模块
│   │   ├── routes.py      # API路由定义
│   │   └── schema.py      # API数据模型
│   ├── config.py          # 应用配置管理
│   ├── logger.py          # 日志配置
│   ├── logs/              # 日志文件目录
│   ├── main.py            # FastAPI应用入口
│   └── tools.py           # 外部工具集成
├── pyproject.toml         # 项目配置和依赖管理
└── uv.lock                # 依赖锁定文件
```

## 🤖 智能体系统

系统采用多智能体架构，包含5个专业智能体，通过智能路由实现用户请求的精准分发：

### 1. 训练计划调整专家
- **职责**: 专业的健身计划定制和调整专家
- **功能**:
  - 运动类型、时长、器械、姿势等多维度计划调整
  - 身体限制部位的智能适配
  - 参数关联关系处理（如椅子瑜伽与坐姿的关联）
  - 智能推荐最接近用户需求的选项
- **工具**: `adjust_workout_plan_tool`

### 2. 健康/运动体验服务专家
- **职责**: 健康咨询和运动体验优化专家
- **功能**:
  - 健康问题咨询和建议
  - 运动感受和体验优化
  - 身体不适的专业指导
  - 运动安全建议

### 3. 订阅/退款服务专家
- **职责**: 处理所有订阅和商务相关问题
- **功能**:
  - 订阅状态查询和管理
  - 取消订阅流程指导
  - 退款申请处理
  - 购买和恢复订阅

### 4. 技术支持专家
- **职责**: 技术支持专家，解决App使用问题
- **功能**:
  - App崩溃和功能异常诊断
  - 技术问题排查和解决
  - 用户使用指导

### 5. 使用指导专家
- **职责**: App使用教程和指导
- **功能**:
  - 注册登录指导
  - 功能使用说明
  - 设置配置帮助

## 🛠️ 技术栈

### 核心框架
- **后端框架**: FastAPI 0.110.0 - 高性能异步Web框架
- **AI框架**: LlamaIndex 0.12.44 - 多智能体工作流框架
- **包管理**: UV - 现代Python包管理工具
- **Web服务器**: Uvicorn 0.28.0 - ASGI服务器

### AI & LLM
- **LLM支持**: OpenAI GPT-4 系列
- **智能体**: 基于LlamaIndex AgentWorkflow的多智能体系统
- **记忆存储**: Redis/Valkey + LlamaIndex Chat Store

### 数据存储与缓存
- **缓存数据库**: Redis 6.1.0 / Valkey-glide 2.0.1 (生产环境)
- **数据序列化**: OrJSON 3.10.18 (高性能JSON处理)
- **记忆管理**: LlamaIndex Chat Store Redis 0.4.1

### 性能与监控
- **请求限流**: SlowAPI - 基于Redis的分布式限流
- **定时任务**: APScheduler 3.10.0 - 异步任务调度
- **超时控制**: 自定义异步超时装饰器
- **健康检查**: 多进程状态监控

### 开发工具
- **日志系统**: Loguru 0.7.3
- **配置管理**: Pydantic Settings 2.9.1
- **环境管理**: Python-dotenv 1.0.0
- **HTTP客户端**: HTTPX - 异步HTTP客户端
- **重试机制**: Tenacity - 智能重试库

### 第三方集成
- **消息推送**: 飞书开放平台API (lark-oapi 1.4.19)
- **告警通知**: Slack Webhook
- **前端支持**: Streamlit 1.37.0 (可选)

## 📋 系统要求

- **Python**: 3.12+ (推荐使用最新版本)
- **Redis/Valkey**: 用于存储用户对话记忆和上下文
- **OpenAI API Key**: 用于LLM服务
- **操作系统**: 支持 macOS、Linux、Windows

## 🚀 核心功能

### API接口
- `POST /v1/api/chat` - 智能对话接口
- `GET /v1/api/health` - 健康检查接口

### 智能体功能
- **健身计划调整**: 支持运动类型、时长、器械、姿势等多维度定制
- **参数关联处理**: 智能处理参数间关联关系（如椅子瑜伽与坐姿关联）
- **智能推荐**: 当用户需求不在标准选项内时，推荐最接近的选项
- **智能路由**: 自动识别用户意图并分发到专业智能体
- **多语言支持**: 根据用户语言偏好提供服务
- **持久化记忆**: 基于Redis/Valkey的对话记忆和上下文管理

### 企业级特性
- **请求限流**: 30次/秒的API限流保护
- **超时控制**: 30秒请求超时保护
- **多进程支持**: 支持4个Worker进程并发处理
- **定时任务**: 数据同步和清理的定时任务
- **健康监控**: 进程状态和定时任务状态监控
- **告警通知**: Slack集成的异常告警

## 🚀 快速开始

### 1. 环境配置

```bash
# 克隆项目
git clone <your-repo-url>
cd ai-coach

# 使用UV安装依赖 (推荐)
uv sync

# 或使用pip安装
pip install -e .

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入必要的配置
```

### 2. 环境变量配置

在 `.env` 文件中配置以下参数：

```bash
# LLM配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4.1-mini-2025-04-14

# 超时配置
AGENT_TIMEOUT=30
API_TIMEOUT=30

# Redis配置
REDIS_HOST=your-redis-host.amazonaws.com
REDIS_PORT=6379
REDIS_DATABASE_URL=redis://localhost:6379
REDIS_TTL=1800
REDIS_CONTEXT_NAME=COACH-SARAH-CONTEXT
KEY_TITLE=COACH-SARAH

# Tool的API配置
V5_URL=https://your-api-domain.com/cmsApp/OOG116/workoutGenerate/v5/plan

# 飞书配置
APP_ID=cli_your_app_id_here
APP_SECRET=your_app_secret_here
APP_TOKEN=your_app_token_here
TABLE_ID=your_table_id_here

# 环境配置
ENV=Local
DEBUG=False

# 定时任务配置
DATA_FETCH_INTERVAL_MINUTES=10
DATA_RETENTION_HOURS=24

# Uvicorn服务器配置
UVICORN_WORKERS=4
UVICORN_HOST=0.0.0.0
UVICORN_PORT=20000
UVICORN_LOG_LEVEL=info

# Slack Webhook
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### 3. 启动服务

```bash
# 启动Redis服务 (如果未运行)
redis-server

# 开发环境启动
uvicorn app.main:app --host 0.0.0.0 --port 20000 --reload

# 生产环境启动 (多进程)
uvicorn app.main:app --host 0.0.0.0 --port 20000 --workers 4
```

### 4. API使用示例

```bash
# 健康检查
curl -X GET "http://localhost:20000/v1/api/health"

# 发送聊天消息
curl -X POST "http://localhost:20000/v1/api/chat" \
     -H "Content-Type: application/json" \
     -d '{
       "query": "我想调整我的健身计划",
       "user_language": "zh",
       "chat_id": "user123_chat",
       "user_workout_plan": {
         "completeTimes": 0,
         "m3u8Type": 10,
         "random": 1,
         "planType": 10,
         "genderCode": 10,
         "coachGenderCode": 10,
         "durationCode": 11,
         "positionCode": 12,
         "restrictionCodeSet": [],
         "exerciseTypeCodeSet": [10, 11],
         "equipmentCodeSet": [10]
       },
       "sub_id": "sub_123",
       "source": "workout_plan"
     }'
```

## 📊 运动计划参数说明

### 基础参数
- **coachGenderCode**: 教练性别 (10:女性, 11:男性, 12:混合)
- **durationCode**: 运动时长 (10:5-10分钟, 11:10-15分钟, 12:15-20分钟, 13:20-30分钟)
- **positionCode**: 运动姿势 (10:站立, 11:坐姿, 12:两者皆可)

### 限制与类型
- **restrictionCodeSet**: 身体限制部位 (10:肩部, 11:腰背部, 12:手腕, 13:膝盖, 14:脚踝, 15:髋部)
- **exerciseTypeCodeSet**: 运动类型 (10:椅子有氧, 11:太极, 12:舞蹈, 13:温和有氧, 14:室内步行, 17:椅子瑜伽)
- **equipmentCodeSet**: 器械类型 (10:无器械, 11:哑铃, 12:阻力带)

### 参数关联规则
- **椅子瑜伽 (17)**: 只有当positionCode为11(坐姿)或12(两者皆可)时才可选择
- **智能推荐**: 当用户需求不在标准选项内时，系统会推荐最接近的选项

## 🏗️ 项目架构

### 多进程架构
- **主进程**: 负责定时任务调度和数据同步
- **工作进程**: 处理API请求和智能体对话
- **进程锁**: 确保定时任务只在主进程中运行

### 数据流
1. **请求接收**: FastAPI接收用户请求
2. **限流检查**: SlowAPI进行请求限流
3. **智能体路由**: 根据用户意图路由到专业智能体
4. **参数处理**: 智能处理运动参数和关联关系
5. **记忆管理**: Redis存储对话历史和上下文
6. **响应返回**: 返回调整后的运动计划和建议

## 🚀 版本化系统 (新增)

### 📋 版本化架构概述

AI Coach 现已支持完整的版本化系统，实现多版本API并行运行：

- **V1版本**：原始设备映射，向后兼容
- **V2版本**：扩展设备映射，支持精细化哑铃重量选择

### 🎯 核心变更

#### 设备映射升级
| V1设备 | V2设备 | 说明 |
|--------|--------|------|
| `Dumbbell` | `Lightweight Dumbbell` | 重新定义为轻量级 |
| - | `Midweight Dumbbell` | 新增中等重量哑铃 |

#### API端点扩展
```bash
# V1接口（保持兼容）
GET  /v1/health
POST /v1/chat

# V2接口（新功能）
GET  /v2/health
POST /v2/chat
GET  /v2/equipment  # 新增设备查询

# 统一接口
GET  /
GET  /versions
POST /migrate
```

### 🔧 开发指南

#### 使用V1接口（现有代码无需修改）

```python
# 现有代码继续工作
from app.agent.agent_tool.mappings import equipment_mapping

print(equipment_mapping)  # V1映射
```

#### 使用V2接口（新功能）

```python
from app.agent.version_manager import APIVersion
from app.agent.agent_tool.versioned_mappings import get_equipment_mapping

# 获取V2设备映射
v2_equipment = get_equipment_mapping(APIVersion.V2)
print(v2_equipment)  # 包含轻量级和中等重量哑铃
```

#### 添加新版本
1. 在 `version_manager.py` 中添加版本枚举
2. 实现版本化映射、工具和Agent类
3. 注册新版本组件
4. 创建对应的API路由

### 🧪 测试和验证

```bash
# 运行版本化系统测试
python run_tests.py

# 运行特定测试
pytest tests/test_version_manager.py -v
pytest tests/test_versioned_mappings.py -v
pytest tests/test_versioned_tools.py -v
pytest tests/test_api_routes.py -v
```

### 📚 文档

- [版本化系统详细文档](docs/VERSION_SYSTEM.md)
- [V1到V2迁移指南](docs/MIGRATION_GUIDE.md)
- [部署指南](docs/DEPLOYMENT_GUIDE.md)

### 监控和调试
- 查看日志: `tail -f app/logs/app.log`
- V1健康检查: `curl http://localhost:20000/v1/health`
- V2健康检查: `curl http://localhost:20000/v2/health`
- 版本信息: `curl http://localhost:20000/versions`
- Redis监控: `redis-cli monitor`