# PromptX 相关目录
.promptx/
.promptx/**

# Frontend 相关目录
frontend/
frontend/**

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
llamaindex_agent_env/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git 相关
.git/
.gitignore
.gitattributes

# 日志文件
*.log
logs/
log/

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 文档构建
docs/_build/
site/

# Node.js (如果有前端构建)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 备份文件
*.bak
*.backup
*.old

# 配置文件（可能包含敏感信息）
config/local.py
config/production.py
secrets.yaml
credentials.json

# Redis 数据文件
dump.rdb
appendonly.aof

# 其他开发工具
.mypy_cache/
.dmypy.json
dmypy.json
.pyre/
.pytype/

# 容器相关
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD 相关
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# 文档和说明
README.md
CHANGELOG.md
LICENSE
*.md

# 示例和测试数据
examples/
sample_data/
test_data/
fixtures/
.promptx
frontend
logs
