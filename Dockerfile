# 使用官方Python 3.12镜像作为基础镜像
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV UV_CACHE_DIR=/app/.uv_cache

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装uv包管理器（更快的Python包管理器）
RUN pip install uv

# 复制项目配置文件
COPY pyproject.toml uv.lock ./

# 使用uv安装Python依赖
RUN uv sync --frozen

# 复制应用代码
COPY app/ ./app/

# 创建必要的目录
RUN mkdir -p /app/logs /app/.uv_cache

# 创建非root用户和必要的目录
RUN groupadd -r appuser && useradd -r -g appuser -m appuser
RUN mkdir -p /home/<USER>/.cache/uv
RUN chown -R appuser:appuser /app /home/<USER>/.cache

# 暴露端口
EXPOSE ${UVICORN_HOST}

# 启动命令 - 直接使用uvicorn，支持环境变量配置
CMD ["sh", "-c", "uv run uvicorn app.main:app --host ${UVICORN_HOST:-0.0.0.0} --port ${UVICORN_PORT:-8000} --workers ${UVICORN_WORKERS:-4} --log-level ${UVICORN_LOG_LEVEL:-info}"]
