# 多样性调整规则

```mermaid
flowchart TD
    A(开始) --> B{{position是否为All position}}
    B -- 否 --> B1[修改position为All position<br/>修改exerciseTypeCodeSet为Chair yoga+Chair cardio+Gentle cardio+Tai chi]
    B1 --> Reply-1[回复:<br/>{self.reply_1_msg}]

    B -- 是 --> C{{exerciseTypeCodeSet是否全选}}
    
    C -- 否 --> D{{exerciseTypeCodeSet是否勾选以下全部类型Chair yoga、Chair cardio、Gentle cardio、Tai chi}}
    D -- 否 --> D1[exerciseTypeCodeSet在**原有基础上**，增加Chair yoga、Chair cardio、Gentle cardio、Tai chi]
    D1 --> Reply-1
    D -- 是 --> D2[exerciseTypeCodeSet**原有基础上**，从Dancing、Indoor walking中随机选取**一个**进行增加]
    D2 --> Reply-1

    C -- 是 --> E{{equipmentCodeSet是否为No Equipment?}}
    E -- 是 --> E1[equipmentCodeSet增加Dumbbell]
    E1 --> Reply-1
    
    E -- 否 --> E2{{equipmentCodeSet是否同时选择了Dumbbell和Resistance Band}}
    E2 -- 否 --> F2[equipmentCodeSet更改为Dumbbell+Resistance Band]
    F2 --> Reply-1
    
    E2 -- 是 --> Reply-2[{self.reply_2_msg}]

    Reply-1 --> End(结束)
    Reply-2 --> End
```