#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片文字提取脚本
使用OCR技术从PNG图片中提取文本内容
"""

import os
import sys
from PIL import Image
import pytesseract
import json

def extract_text_from_image(image_path):
    """从图片中提取文本"""
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return {"error": f"文件不存在: {image_path}"}
        
        # 打开图片
        image = Image.open(image_path)
        
        # 使用OCR提取文本
        text = pytesseract.image_to_string(image, lang='chi_sim+eng')
        
        return {
            "success": True,
            "text": text.strip(),
            "file": image_path,
            "size": image.size
        }
        
    except Exception as e:
        return {"error": str(e)}

def analyze_text_for_mermaid(text):
    """分析提取的文本，尝试识别可能的mermaid图表结构"""
    lines = text.split('\n')
    
    # 简单的图表结构识别
    flowchart_indicators = ['开始', '结束', '流程', '步骤', '判断', '是', '否']
    sequence_indicators = ['用户', '系统', '请求', '响应', '调用']
    
    chart_type = "unknown"
    
    # 检查可能的图表类型
    if any(indicator in text for indicator in flowchart_indicators):
        chart_type = "flowchart"
    elif any(indicator in text for indicator in sequence_indicators):
        chart_type = "sequence"
    
    return {
        "chart_type": chart_type,
        "lines": len(lines),
        "content": lines[:10]  # 前10行
    }

def main():
    image_path = "/Users/<USER>/development/CoreAppDev/ai-coach/image.png"
    
    print("正在提取图片中的文本...")
    result = extract_text_from_image(image_path)
    
    if "error" in result:
        print(f"错误: {result['error']}")
        return
    
    print(f"成功提取文本，共 {len(result['text'])} 字符")
    print("\n提取的文本内容:")
    print("-" * 50)
    print(result['text'])
    print("-" * 50)
    
    # 分析文本结构
    analysis = analyze_text_for_mermaid(result['text'])
    print(f"\n分析结果:")
    print(f"可能的图表类型: {analysis['chart_type']}")
    print(f"总行数: {analysis['lines']}")
    print(f"前10行预览: {analysis['content']}")
    
    # 保存结果到文件
    output_file = "/Users/<USER>/development/CoreAppDev/ai-coach/extracted_text.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(result['text'])
    
    print(f"\n文本已保存到: {output_file}")

if __name__ == "__main__":
    main()