import os
import sys
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict
import inspect

from app.config import settings
from loguru import logger

# 存储每个模块的logger配置状态
_configured_modules: Dict[str, bool] = {}
# 全局控制台配置状态
_console_configured = False


def cleanup_old_logs(base_log_dir: Path, days_to_keep: int = 5):
    """
    清理指定天数之前的日志文件夹
    :param base_log_dir: 日志基础目录
    :param days_to_keep: 保留的天数，默认5天
    """
    try:
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        for item in base_log_dir.iterdir():
            if item.is_dir() and item.name.isdigit() and len(item.name) == 8:  # YYYYMMDD格式
                try:
                    folder_date = datetime.strptime(item.name, "%Y%m%d")
                    if folder_date < cutoff_date:
                        shutil.rmtree(item)
                        print(f"已删除过期日志文件夹: {item}")
                except ValueError:
                    # 如果文件夹名不是有效的日期格式，跳过
                    continue
    except Exception as e:
        print(f"清理日志时发生错误: {e}")


def get_logger(log_name: Optional[str] = None):
    """
    获取独立的日志实例，每个脚本都有自己的日志文件。

    需求：
    1. 不同的脚本日志保存在不同的.log文件中，如果不存在则新建
    2. 每天新建一个目录保存当天的日志，如果不存在则新建
    3. 自动删除5天前的日志目录

    :param log_name: 日志文件名（不带路径和扩展名），默认使用调用文件名
    """
    global _console_configured

    # 自动获取调用者脚本名
    if log_name is None:
        caller_frame = inspect.stack()[1]
        caller_path = Path(caller_frame.filename)
        log_name = caller_path.stem  # 不带后缀的脚本名

    # 如果该模块已经配置过，直接返回全局logger（已配置了该模块的文件输出）
    if log_name in _configured_modules:
        return logger

    # 日志基础目录：项目根目录下 logs/（Docker环境下为 /app/logs）
    if Path("/app/logs").exists():
        # Docker环境，使用挂载的日志目录
        base_log_dir = Path("/app/logs")
    else:
        # 本地环境，使用相对路径
        base_log_dir = Path(__file__).resolve().parent / "logs"

    base_log_dir.mkdir(parents=True, exist_ok=True)

    # 按天创建日志文件夹（格式：YYYYMMDD）
    today = datetime.now().strftime("%Y%m%d")
    daily_log_dir = base_log_dir / today
    daily_log_dir.mkdir(parents=True, exist_ok=True)

    # 清理5天前的日志（只在第一次调用时执行）
    if not _configured_modules:
        cleanup_old_logs(base_log_dir, days_to_keep=5)

    # 日志文件路径：logs/YYYYMMDD/脚本名.log
    log_file_path = daily_log_dir / f"{log_name}.log"

    # 判断日志level
    if settings.DEBUG is False:
        level = "INFO"
    else:
        level = "DEBUG"

    # 配置控制台输出（只配置一次）
    if not _console_configured:
        # 移除默认配置
        logger.remove()

        # 添加控制台输出
        logger.add(
            sys.stderr,
            level=level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}:{line}</cyan> - <level>{message}</level>"
        )
        _console_configured = True

    # 为该模块添加独立的文件输出，使用过滤器确保只记录来自该模块的日志
    def create_module_filter(target_module_name):
        def module_filter(record):
            # 获取日志记录的文件路径
            record_file = Path(record["file"].name)
            record_module = record_file.stem

            # 处理模块路径，如 app.agent.agent_init -> agent_init
            if "." in target_module_name:
                target_stem = target_module_name.split(".")[-1]
            else:
                target_stem = target_module_name

            # 只有当日志来自目标模块时才写入该模块的日志文件
            return record_module == target_stem

        return module_filter

    logger.add(
        log_file_path,
        rotation="10 MB",  # 超过大小自动轮转
        encoding="utf-8",
        enqueue=True,  # 多进程安全
        level=level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{line} - {message}",
        filter=create_module_filter(log_name)
    )

    # 标记该模块已配置
    _configured_modules[log_name] = True

    return logger
