from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse

from app.agent.agent_chat import multi_agent_chat
from app.agent.version_manager.version_manager import APIVersion
from app.api.v1.schema import ChatRequest
from app.api.common import (
    timeout, limiter, v1_rate_limit_handler,
    get_worker_info, RateLimits, Timeouts
)
from app.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


# 使用公共的限速处理器
custom_rate_limit_handler = v1_rate_limit_handler


@router.get("/health")
@limiter.limit(RateLimits.HEALTH_CHECK)
@timeout(Timeouts.HEALTH_CHECK)
async def health_check(request: Request):
    """健康检查端点，包含worker和定时任务状态信息"""
    worker_info = get_worker_info()
    return {
        "status": "healthy",
        **worker_info
    }


@router.post("/chat")
@limiter.limit(RateLimits.CHAT_API)
@timeout(Timeouts.CHAT_API)
async def chat(chat_request: ChatRequest, request: Request) -> JSONResponse:
    """V1版本执行AI对话的接口"""
    chat_id = chat_request.chat_id
    query = chat_request.query
    user_langauge = chat_request.user_language
    user_workout_plan = chat_request.user_workout_plan
    sub_id = chat_request.sub_id
    source = chat_request.source
    logger.info(
        f"收到v1非流式聊天请求: chat_id={chat_id}, query={query}, source={source}"
    )
    try:
        response = await multi_agent_chat(
            chat_id=chat_id,
            user_query=query,
            user_language=user_langauge,
            user_workout_plan=user_workout_plan,
            sub_id=sub_id,
            source=source,
            api_version=APIVersion.V1
        )
        return JSONResponse(content=response)
    except Exception as e:
        logger.error(f"聊天处理失败: user_id={chat_request.chat_id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"v1 聊天处理失败: {str(e)}") from e
