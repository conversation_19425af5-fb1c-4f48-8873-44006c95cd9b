from typing import List

from pydantic import BaseModel, Field


class UserModel(BaseModel):
    """用户的基础数据模型"""
    user_id: str = Field(description="用户唯一标识符")


class ChatIdModel(BaseModel):
    """聊天会话ID模型"""
    chat_id: str = Field(description="聊天会话唯一标识符")


class WorkoutPlanModel(BaseModel):
    """运动计划配置模型"""
    completeTimes: int = Field(description="计划完成次数")
    m3u8Type: int = Field(description="视频流类型编码")
    random: int = Field(description="随机化参数")
    planType: int = Field(description="计划类型编码")
    genderCode: int = Field(description="用户性别编码")

    coachGenderCode: int = Field(description="教练性别编码，10:女性教练, 11:男性教练, 12:混合性别教练")
    durationCode: int = Field(description="运动时长编码，10:5-10分钟, 11:10-15分钟, 12:15-20分钟, 13:20-30分钟")
    positionCode: int = Field(description="运动姿势编码，10:站立, 11:坐姿, 12:两者皆可")

    restrictionCodeSet: List[int] = Field(
        description="身体限制部位编码，10:肩部, 11:腰背部, 12:手腕, 13:膝盖, 14:脚踝, 15:髋部")
    exerciseTypeCodeSet: List[int] = Field(
        description="运动类型编码集合，10:椅子有氧, 11:太极, 12:舞蹈, 13:温和有氧, 14:室内步行, 17:椅子瑜伽")
    equipmentCodeSet: List[int] = Field(description="器械编码集合，10:无器械, 11:哑铃, 12:阻力带")


class ChatRequest(BaseModel):
    """聊天请求模型"""
    query: str = Field(description="用户查询内容")
    user_language: str = Field(description="用户语言偏好")
    chat_id: str = Field(description="聊天会话唯一标识符")
    user_workout_plan: WorkoutPlanModel = Field(description="用户运动计划配置信息")
    sub_id: str = Field(description="用户的Sub ID")
    source: str = Field(description="用户触发对话的页面")
