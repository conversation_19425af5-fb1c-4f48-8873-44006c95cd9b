"""
API公共组件 - 超时装饰器、限速器等共享功能

主要功能：
1. 统一的超时装饰器
2. 共享的限速器配置
3. 通用的错误处理器
4. API工具函数

作者：Python专家
创建时间：2025-01-17
"""

import os
import asyncio
from functools import wraps
from typing import Callable, Any
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from app.logger import get_logger
from app.utilities import slack

logger = get_logger(__name__)


# ===== 超时装饰器 =====
def timeout(seconds: int):
    """
    接口超时装饰器
    :param seconds: 超时时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
            except asyncio.TimeoutError:
                logger.error(f"接口 {func.__name__} 处理超时 ({seconds}秒)")
                raise HTTPException(
                    status_code=408,
                    detail=f"请求处理超时，连接已关闭 (超时时间: {seconds}秒)",
                )
        return wrapper
    return decorator


# ===== slowapi 限速器配置 =====
limiter = Limiter(key_func=get_remote_address)


def create_rate_limit_handler(api_version: str = ""):
    """
    创建版本化的限速异常处理器
    :param api_version: API版本标识
    """
    async def custom_rate_limit_handler(request: Request, exc: RateLimitExceeded):
        """自定义限速异常处理器"""
        client_ip = request.client.host
        endpoint = request.url.path
        version_info = f" {api_version}" if api_version else ""
        
        logger.warning(f"Rate limit exceeded{version_info} - IP: {client_ip}, Endpoint: {endpoint}")

        message = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"OOG116 AI Coach{version_info} Rate Warning",
                    "emoji": True,
                },
            },
            {
                "type": "rich_text",
                "elements": [
                    {
                        "type": "rich_text_list",
                        "style": "bullet",
                        "indent": 0,
                        "elements": [
                            {
                                "type": "rich_text_section",
                                "elements": [
                                    {"type": "text", "text": f"API Version: {api_version or 'Unknown'}"},
                                ],
                            },
                            {
                                "type": "rich_text_section",
                                "elements": [
                                    {"type": "text", "text": f"IP: {client_ip}"},
                                ],
                            },
                            {
                                "type": "rich_text_section",
                                "elements": [
                                    {"type": "text", "text": f"Endpoint: {endpoint}"},
                                ],
                            },
                        ],
                    }
                ],
            },
        ]

        # 发送Slack通知
        try:
            slack.post_slack_msg(message=message)
        except Exception as e:
            logger.error(f"发送Slack通知失败: {e}")

        # 安全获取retry_after属性
        retry_after = getattr(exc, 'retry_after', 60)  # 默认60秒

        # 返回统一格式的错误响应
        error_response = {
            "error": "RateLimitExceeded",
            "message": "请求频率超限，请稍后重试",
            "api_version": api_version or "unknown",
            "details": {"retry_after": retry_after},
            "suggestions": ["请降低请求频率", "等待一段时间后重试"]
        }
        
        return JSONResponse(
            status_code=429,
            content=error_response
        )
    
    return custom_rate_limit_handler


# ===== 工具函数 =====
def is_main_worker_check() -> bool:
    """检查是否为主工作进程"""
    worker_id = os.getenv("UVICORN_WORKER_ID", "main")
    return worker_id == "main"


def get_worker_info() -> dict:
    """获取工作进程信息"""
    worker_id = os.getenv("UVICORN_WORKER_ID", "main")
    current_pid = os.getpid()
    is_main = is_main_worker_check()
    
    return {
        "worker_id": worker_id,
        "process_id": current_pid,
        "is_main_worker": is_main,
        "scheduler_enabled": is_main,
        "message": f"Worker {worker_id} (PID: {current_pid}) is running"
                  + (" with scheduler" if is_main else " without scheduler")
    }


# ===== 常用限速配置 =====
class RateLimits:
    """常用的限速配置"""
    HEALTH_CHECK = "30/second"      # 健康检查：每秒30次
    CHAT_API = "30/second"          # 聊天接口：每秒30次  
    INFO_API = "60/second"          # 信息查询：每秒60次
    HEAVY_API = "10/second"         # 重型接口：每秒10次


# ===== 常用超时配置 =====
class Timeouts:
    """常用的超时配置"""
    HEALTH_CHECK = 5                # 健康检查：5秒
    CHAT_API = 30                   # 聊天接口：30秒
    INFO_API = 3                    # 信息查询：3秒
    HEAVY_API = 60                  # 重型接口：60秒


# 创建默认的限速处理器
default_rate_limit_handler = create_rate_limit_handler()
v1_rate_limit_handler = create_rate_limit_handler("V1")
v2_rate_limit_handler = create_rate_limit_handler("V2")
