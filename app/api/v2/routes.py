"""
V2 API Routes - 扩展设备支持的API接口

主要功能：
1. V2版本的聊天接口
2. 扩展的设备映射支持
3. 向后兼容性保证
4. 版本化错误处理

作者：Python专家
创建时间：2025-01-17
"""

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse

from app.agent.agent_chat import multi_agent_chat
from app.api.v2.schema import ChatRequestV2
from app.agent.version_manager.version_manager import APIVersion
from app.api.common import (
    timeout, limiter, v2_rate_limit_handler,
    get_worker_info, RateLimits, Timeouts
)
from app.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 使用公共的限速处理器
custom_rate_limit_handler_v2 = v2_rate_limit_handler


@router.get("/health")
@limiter.limit(RateLimits.HEALTH_CHECK)
@timeout(Timeouts.HEALTH_CHECK)
async def health_check_v2(request: Request):
    """V2版本健康检查端点"""
    worker_info = get_worker_info()
    return {
        "status": "healthy",
        **worker_info
    }


@router.post("/chat")
@limiter.limit(RateLimits.CHAT_API)
@timeout(Timeouts.CHAT_API)
async def chat_v2(chat_request: ChatRequestV2, request: Request) -> JSONResponse:
    """V2版本AI对话接口 - 支持扩展设备映射"""
    chat_id = chat_request.chat_id
    query = chat_request.query
    user_language = chat_request.user_language
    user_workout_plan = chat_request.user_workout_plan
    sub_id = chat_request.sub_id
    source = chat_request.source

    logger.info(
        f"收到V2聊天请求: chat_id={chat_id}, query={query}, source={source}"
    )

    try:
        response = await multi_agent_chat(
            chat_id=chat_id,
            user_query=query,
            user_language=user_language,
            user_workout_plan=user_workout_plan,
            sub_id=sub_id,
            source=source,
            api_version=APIVersion.V2
        )
        return JSONResponse(content=response)

    except Exception as e:
        logger.error(f"V2聊天处理失败: user_id={chat_id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"v2聊天处理失败: {str(e)}") from e
