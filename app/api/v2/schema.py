"""
V2 API Schema - 扩展的请求和响应模型

主要功能：
1. V2版本的请求模型
2. 扩展的设备支持
3. 向后兼容性
4. 版本化验证

作者：Python专家
创建时间：2025-01-17
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class WorkoutPlanV2(BaseModel):
    """V2版本运动计划响应模型"""
    completeTimes: int = Field(description="计划完成次数")
    m3u8Type: int = Field(description="视频流类型编码")
    random: int = Field(description="随机化参数")
    planType: int = Field(description="计划类型编码")
    genderCode: int = Field(description="用户性别编码")

    coachGenderCode: int = Field(description="教练性别编码，10:女性教练, 11:男性教练, 12:混合性别教练")
    durationCode: int = Field(description="运动时长编码，10:5-10分钟, 11:10-15分钟, 12:15-20分钟, 13:20-30分钟")
    positionCode: int = Field(description="运动姿势编码，10:站立, 11:坐姿, 12:两者皆可")

    restrictionCodeSet: List[int] = Field(
        description="身体限制部位编码，10:肩部, 11:腰背部, 12:手腕, 13:膝盖, 14:脚踝, 15:髋部")
    exerciseTypeCodeSet: List[int] = Field(
        description="运动类型编码集合，10:椅子有氧, 11:太极, 12:舞蹈, 13:温和有氧, 14:室内步行, 17:椅子瑜伽")
    equipmentCodeSet: List[int] = Field(description="器械编码集合，10:无器械, 11:2-6磅哑铃, 12:阻力带, 15:6-10磅哑铃")


class ChatRequestV2(BaseModel):
    """V2版本聊天请求模型 - 扩展设备支持"""

    chat_id: str = Field(..., description="聊天会话ID")
    query: str = Field(..., description="用户查询内容")
    user_language: str = Field(default="zh", description="用户语言设置")
    user_workout_plan: WorkoutPlanV2 = Field(default=None, description="用户运动计划")
    sub_id: Optional[str] = Field(default=None, description="订阅ID")
    source: Optional[str] = Field(default=None, description="请求来源")


class HealthCheckResponseV2(BaseModel):
    """V2版本健康检查响应模型"""

    status: str = Field(..., description="服务状态")
    api_version: str = Field(default="v2", description="API版本")
    worker_id: str = Field(..., description="工作进程ID")
    process_id: int = Field(..., description="进程ID")
    is_main_worker: bool = Field(..., description="是否为主工作进程")
    scheduler_enabled: bool = Field(..., description="调度器是否启用")
    message: str = Field(..., description="状态消息")
    version_info: Dict[str, Any] = Field(..., description="版本信息")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "api_version": "v2",
                "worker_id": "main",
                "process_id": 12345,
                "is_main_worker": True,
                "scheduler_enabled": True,
                "message": "Worker main (PID: 12345) is running with scheduler",
                "version_info": {
                    "current_version": "v2",
                    "supported_equipment": [
                        "No Equipment",
                        "Lightweight Dumbbell",
                        "Resistance Band",
                        "Midweight Dumbbell"
                    ],
                    "new_features": [
                        "精细化哑铃重量选择",
                        "更个性化的训练建议"
                    ]
                }
            }
        }


class ErrorResponseV2(BaseModel):
    """V2版本错误响应模型"""

    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    api_version: str = Field(default="v2", description="API版本")
    details: Optional[Dict[str, Any]] = Field(default=None, description="错误详情")
    suggestions: Optional[List[str]] = Field(default=None, description="解决建议")

    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "V2版本不支持的设备类型",
                "api_version": "v2",
                "details": {
                    "invalid_equipment": "Dumbbell",
                    "valid_equipment": [
                        "No Equipment",
                        "Lightweight Dumbbell",
                        "Resistance Band",
                        "Midweight Dumbbell"
                    ]
                },
                "suggestions": [
                    "请使用 'Lightweight Dumbbell' 替代 'Dumbbell'",
                    "或选择 'Midweight Dumbbell' 进行更高强度训练"
                ]
            }
        }
