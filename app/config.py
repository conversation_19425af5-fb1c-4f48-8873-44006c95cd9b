import os
import json
from pathlib import Path
from typing import List, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv


class Settings(BaseSettings):
    """应用配置类"""

    # 加载环境变量
    load_dotenv()

    ENV_PATH: Path = Path(__file__).resolve().parents[1] / ".env"

    # LLM配置
    GOOGLE_API_KEY: str = os.getenv("GOOGLE_API_KEY", "")
    GOOGLE_MODEL: str = os.getenv("GOOGLE_MODEL", "")
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "")

    # 超时配置
    AGENT_TIMEOUT: int = os.getenv("AGENT_TIMEOUT", 30)
    API_TIMEOUT: int = os.getenv("API_TIMEOUT", 30)

    # Redis配置
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "")  # Redis密码，为空表示无密码
    REDIS_PORT: int = os.getenv("REDIS_PORT", 6379)
    REDIS_TTL: int = int(os.getenv("REDIS_TTL", "3600"))  # Context TTL默认保存1小时
    REDIS_CONTEXT_NAME: str = os.getenv("REDIS_CONTEXT_NAME", "")
    KEY_TITLE: str = os.getenv("KEY_TITLE", "COACH-SARAH")

    # Tool的API配置 - 使用字符串类型避免Pydantic解析问题
    URL_MAP_STR: str = os.getenv("URL_MAP", '{"v1": "https://core-app-test-large.7mfitness.com/cmsApp/OOG116/workoutGenerate/v5/plan", "v2": "https://core-app-test-large.7mfitness.com/cmsApp/OOG116/workoutGenerate/v5/plan"}')

    @property
    def URL_MAP(self) -> Dict:
        """解析URL_MAP字符串为字典"""
        try:
            return json.loads(self.URL_MAP_STR)
        except json.JSONDecodeError:
            # 如果解析失败，返回默认值
            return {
                "v1": "https://core-app-test-large.7mfitness.com/cmsApp/OOG116/workoutGenerate/v5/plan",
                "v2": "https://core-app-test-large.7mfitness.com/cmsApp/OOG116/workoutGenerate/v5/plan"
            }

    # Workout Plan的一些配置
    STR_KEY_LIST: List[str] = ["durationCode", "coachGenderCode", "positionCode"]
    LIST_KEY_LIST: List[str] = ["exerciseTypeCodeSet", "equipmentCodeSet", "restrictionCodeSet"]

    # 环境配置
    ENV: str = os.getenv("ENV", "Local")
    DEBUG: bool = os.getenv("DEBUG", False)

    # APScheduler配置
    SCHEDULER_TIMEZONE: str = os.getenv("SCHEDULER_TIMEZONE", "Asia/Shanghai")
    SCHEDULER_JOB_DEFAULTS: dict = {
        'coalesce': True,  # 合并多个相同的作业
        'max_instances': 1,  # 同一时间只允许一个实例运行
        'misfire_grace_time': 30  # 错过执行时间的宽限期（秒）
    }

    # 数据服务配置
    DATA_FETCH_INTERVAL_MINUTES: int = os.getenv("DATA_FETCH_INTERVAL_MINUTES", 30)
    DATA_RETENTION_HOURS: int = os.getenv("DATA_RETENTION_HOURS", 24)

    # 飞书配置
    APP_ID: str = os.getenv("APP_ID", "")
    APP_SECRET: str = os.getenv("APP_SECRET", "")
    APP_TOKEN: str = os.getenv("APP_TOKEN", "")
    FALLBACK_RECORD_COUNT: int = os.getenv("FALLBACK_RECORD_COUNT", 1000)
    SYNC_TIME: int = os.getenv("SYNC_TIME", 30)

    # 定时任务配置
    SCHEDULER_TIME: int = os.getenv("SCHEDULER_TIME", 10)

    # Slack Webhook
    SLACK_WEBHOOK: str = os.getenv("SLACK_WEBHOOK", "")


# 创建全局配置实例
settings = Settings()
