import asyncio
from datetime import datetime

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED

from app.config import settings
from app.logger import get_logger
from app.services.sync_lark_service import SyncLarkService

logger = get_logger(__name__)


class SimpleScheduledService:
    """基于APScheduler的定时服务"""

    def __init__(self, interval_minutes: int = None):
        self.data_service = SyncLarkService()
        self.interval_minutes = interval_minutes or settings.DATA_FETCH_INTERVAL_MINUTES
        self.scheduler = AsyncIOScheduler(
            timezone=settings.SCHEDULER_TIMEZONE,
            job_defaults=settings.SCHEDULER_JOB_DEFAULTS
        )
        self.job_id = 'data_fetch_task'

        # 添加任务监听器
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)

    async def start_scheduled_task(self):
        """启动定时任务"""
        if self.scheduler.running:
            logger.warning("定时任务已在运行中")
            return

        try:
            # 添加间隔任务
            self.scheduler.add_job(
                self._run_task,
                trigger=IntervalTrigger(minutes=self.interval_minutes),
                id=self.job_id,
                name='数据获取任务',
                max_instances=1,  # 防止重复执行
                coalesce=True,  # 合并错过的任务
                misfire_grace_time=300,  # 5分钟容错时间
                replace_existing=True
            )

            # 启动调度器
            self.scheduler.start()
            logger.info(f"定时数据获取任务已启动，间隔: {self.interval_minutes}分钟")

        except Exception as e:
            logger.error(f"启动定时任务失败: {e}")
            raise

    async def stop_scheduled_task(self):
        """停止定时任务"""
        if not self.scheduler.running:
            logger.warning("定时任务未在运行")
            return

        try:
            self.scheduler.shutdown(wait=True)
            logger.info("定时数据获取任务已停止")
        except Exception as e:
            logger.error(f"停止定时任务失败: {e}")

    async def _run_task(self):
        """执行数据获取任务，然后同步至飞书"""
        try:
            logger.info("开始执行定时数据同步任务")

            # 获取并格式化数据
            await self.data_service.get_redis_client()
            await self.data_service.sync_data()

            logger.info(f"定时任务完成")

            # 这里可以添加你需要的数据处理逻辑
            # 例如：保存到文件、发送到其他系统等

            return True
        except Exception as e:
            logger.error(f"定时数据获取任务执行失败: {e}")
            raise
        finally:
            await self.data_service.close_redis_connection()

    def _job_listener(self, event):
        """任务执行监听器"""
        if event.exception:
            logger.error(f"任务执行出错: {event.exception}")
        else:
            logger.debug(f"任务执行成功: {event.job_id}")

    def add_cron_job(self, cron_expression: str):
        """添加基于cron表达式的任务"""
        try:
            # 移除现有的间隔任务
            if self.scheduler.get_job(self.job_id):
                self.scheduler.remove_job(self.job_id)

            # 添加cron任务
            self.scheduler.add_job(
                self._run_task,
                trigger=CronTrigger.from_crontab(cron_expression),
                id=f'{self.job_id}_cron',
                name='数据获取任务(Cron)',
                max_instances=1,
                coalesce=True,
                misfire_grace_time=300,
                replace_existing=True
            )

            logger.info(f"已添加Cron任务: {cron_expression}")

        except Exception as e:
            logger.error(f"添加Cron任务失败: {e}")
            raise

    def get_job_info(self):
        """获取任务信息"""
        jobs = self.scheduler.get_jobs()
        job_info = []

        for job in jobs:
            info = {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            }
            job_info.append(info)

        return job_info

    async def run_once(self):
        """手动执行一次任务"""
        logger.info("手动执行数据获取任务")
        return await self._run_task()
