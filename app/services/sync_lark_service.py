import asyncio
import json
import time
import copy
from typing import List, Dict, Any, Optional

from app.logger import get_logger
from app.utilities.lark import Lark
from app.config import settings
from app.redis_server.redis_adapter import create_redis_adapter, RedisAdapter
from app.redis_server.redis_client import get_client, close_client

logger = get_logger(__name__)


class SyncLarkService:
    """
    对话历史同步飞书
    """

    def __init__(self):
        self.lark = Lark()
        self.timestamp_index_key = f"{settings.KEY_TITLE}-timestamp-index"
        self.redis_client: Optional[RedisAdapter] = None

    async def get_redis_client(self):
        """获取Redis客户端实例"""
        if self.redis_client is None:
            raw_client = await get_client()
            if raw_client is None:
                logger.error("无法获取Redis客户端")
                raise ConnectionError("Redis客户端连接失败")
            self.redis_client = create_redis_adapter(raw_client)
        return self.redis_client

    async def close_redis_connection(self):
        """关闭Redis连接"""
        if self.redis_client:
            if hasattr(self.redis_client, "client"):
                # 如果是适配器，获取底层客户端
                raw_client = self.redis_client.client
                await close_client(raw_client)
            self.redis_client = None

    def _get_table_id(self):
        table_info_list = self.lark.list_app_table(app_token=settings.APP_TOKEN)
        new_table_num = 0
        table_id = ""
        # 获取最新表格的id
        for table_info in table_info_list["items"]:
            _table_id = table_info["table_id"]
            table_name: str = table_info["name"]
            table_num = int(table_name.split(" - ")[-1])
            new_table_num, table_id = table_num, (
                _table_id
                if table_num > new_table_num or new_table_num == 0
                else new_table_num
            )

        table_record_num = self.lark.get_table_records_number(
            app_token=settings.APP_TOKEN, table_id=table_id
        )

        if table_record_num > 20000 - settings.FALLBACK_RECORD_COUNT:
            table_name = f"数据表 - {new_table_num + 1}"
            logger.info(f"新建数据表: {table_name}")
            table_field = [
                {"field_name": "Sub ID", "type": 1},
                {"field_name": "Chat", "type": 1},
                {
                    "field_name": "Date",
                    "type": 5,
                    "property": {
                        "date_formatter": "yyyy/MM/dd HH:mm",
                        "auto_fill": False,
                    },
                },
            ]

            table_id = self.lark.create_app_table(
                app_token=settings.APP_TOKEN,
                table_name=table_name,
                tabel_fields=table_field,
            )
            return table_id

        return table_id

    async def _get_old_data(self, minutes_ago: int = settings.SYNC_TIME) -> List:
        """获取指定时间前的聊天数据"""

        cutoff_time_ms = int((time.time() - minutes_ago * 60) * 1000)
        # 从Sorted Set中获取旧数据的键
        old_keys_with_scores = await self.redis_client.zrangebyscore(
            self.timestamp_index_key, 0, cutoff_time_ms, withscores=True
        )
        logger.debug(f"Old keys with scores: {old_keys_with_scores}")
        old_data = []

        for redis_key, timestamp in old_keys_with_scores:
            try:
                data = await self.redis_client.hgetall(redis_key)
                if data and redis_key.startswith(f"{settings.KEY_TITLE}-Memory-"):
                    old_data.append(data)
            except Exception as e:
                logger.error(f"获取对话历史失败, Error: {e}")

        return old_data

    def _format_old_data(self, old_data_list: List):
        format_old_data = []
        for old_data in old_data_list:
            chat_data = old_data[b"data"].decode("utf-8")
            chat_data = json.loads(chat_data)
            messages = chat_data["messages"]
            message_str = ""
            for message in messages:
                role = message["role"]
                content = message["content"]
                message_str += f"{role}: {content}\n"

            sub_id = chat_data.get("sub_id", "")
            last_updated = chat_data["last_updated"]
            chat_id = chat_data["chat_id"]
            source = chat_data["source"]

            format_old_data.append(
                {"Sub ID": sub_id, "Chat": message_str, "Date": last_updated, "Source": source}
            )

            old_data["chat_id"] = chat_id

        return format_old_data, old_data_list

    async def sync_data(self):
        """事务性同步数据：获取->删除->同步->失败时回填"""
        # 获取需要同步的数据
        history_data_list = await self._get_old_data()

        if not history_data_list:
            logger.info("没有需要同步的数据")
            return

        logger.info(f"获取到 {len(history_data_list)} 条待同步数据")

        # 获取飞书Table ID
        table_id = self._get_table_id()
        logger.info(f"Lark Table ID: {table_id}")

        # 按照300进行分批处理
        for i in range(0, len(history_data_list), 300):
            old_data_group = history_data_list[i: i + 300]
            format_old_data_group, old_data_group = self._format_old_data(old_data_group)
            # 上传飞书
            result = False
            try:
                result = self.lark.batch_create_table_records(
                    app_token=settings.APP_TOKEN,
                    table_id=table_id,
                    record_data=format_old_data_group,
                )
            except Exception as e:
                logger.error(f"数据同步飞书步骤失败, Error: {e}")

            # 判断是否需要删除数据
            if result:
                # 上传成功，删除Redis中的数据
                try:
                    await self._delete_redis_data_batch(old_data_group)
                    logger.info(
                        f"成功删除第 {i // 300 + 1} 批数据，共 {len(old_data_group)} 条"
                    )
                except Exception as delete_error:
                    logger.error(
                        f"删除第 {i // 300 + 1} 批Redis数据失败: {delete_error}"
                    )
                    # 删除失败时，数据仍在Redis中，下次同步时会重新处理
            else:
                logger.warning(
                    f"第 {i // 300 + 1} 批数据上传失败，保留Redis数据，共 {len(old_data_group)} 条"
                )

        logger.info("数据同步流程完成")

    async def _delete_redis_data_batch(self, data_items: List[Dict[str, Any]]) -> None:
        """批量删除Redis中的数据项"""
        try:
            deleted_count = 0
            for data_item in data_items:
                data_item = data_item
                chat_id = data_item["chat_id"]
                redis_key = f"{settings.KEY_TITLE}-Memory-{chat_id}"
                logger.debug(f"开始删除Redis数据: {redis_key}")

                # 删除Hash数据
                hash_deleted = await self.redis_client.delete(redis_key)

                # 从时间戳索引中移除
                index_deleted = await self.redis_client.zrem(
                    self.timestamp_index_key, redis_key
                )

                if hash_deleted and index_deleted:
                    deleted_count += 1
                    logger.debug(f"删除Redis数据: {redis_key}")
                else:
                    logger.warning(f"Redis数据不存在或已被删除: {redis_key}")

            logger.info(f"批量删除Redis数据完成，实际删除 {deleted_count} 条")

        except Exception as e:
            logger.error(f"批量删除Redis数据失败: {e}")
            raise


async def main():
    client = SyncLarkService()
    await client.get_redis_client()
    await client.sync_data()
    await client.close_redis_connection()


if __name__ == "__main__":
    asyncio.run(main())
