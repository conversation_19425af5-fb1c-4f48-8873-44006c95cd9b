from typing import Dict

from glide import ExpirySet, ExpiryType, RangeByScore, ScoreBoundary

from app.logger import get_logger

logger = get_logger(__name__)


class RedisAdapter:
    """Redis客户端适配器，统一不同Redis客户端的接口"""

    def __init__(self, redis_client):
        self.client = redis_client
        self.client_type = self._detect_client_type()
        logger.info(f"Redis客户端类型: {self.client_type}")

    def _detect_client_type(self) -> str:
        """检测Redis客户端类型"""
        module_name = self.client.__class__.__module__
        class_name = self.client.__class__.__name__

        if 'glide' in module_name:
            return 'valkey-glide'
        elif 'redis' in module_name:
            return 'redis-py'
        else:
            logger.warning(f"未知的Redis客户端类型: {module_name}.{class_name}")
            return 'unknown'

    async def get(self, key: str):
        """统一的get接口"""
        return await self.client.get(key)

    async def set(self, key: str, value: str, ex: int = None):
        """统一的set接口"""
        if ex:
            if self.client_type == 'valkey-glide':
                return await self.client.set(key, value, expiry=ExpirySet(ExpiryType.SEC, ex))
            elif self.client_type == "redis-py":
                return await self.client.set(key, value, ex=ex)
            else:
                try:
                    return await self.client.set(key, value, ex=ex)
                except Exception as e:
                    logger.error(f"Unknown Redis set exception: {e}")
        else:
            return await self.client.set(key, value)

    async def delete(self, key: str):
        """统一的delete接口"""
        if self.client_type == 'valkey-glide':
            return await self.client.delete([key])
        elif self.client_type == 'redis-py':
            return await self.client.delete(key)
        else:
            try:
                return await self.client.delete(key)
            except Exception as e:
                logger.error(f"Unknown Redis Delete Exception: {e}")

    async def hset(self, key: str, field: str = None, value: str = None, mapping: Dict[str, str] = None):
        """统一的hset接口"""
        try:
            if self.client_type == 'redis-py':
                if mapping:
                    # redis-py支持mapping参数
                    return await self.client.hset(key, mapping=mapping)
                else:
                    # redis-py也支持field-value方式
                    return await self.client.hset(key, field, value)

            elif self.client_type == 'valkey-glide':
                if mapping:
                    # valkey-glide使用字典格式: hset(key, field_value_map)
                    return await self.client.hset(key, mapping)
                else:
                    # valkey-glide的单字段设置，也使用字典格式
                    return await self.client.hset(key, {field: value})

            else:
                # 未知客户端，尝试标准方式
                if mapping:
                    # 尝试mapping方式
                    try:
                        return await self.client.hset(key, mapping=mapping)
                    except TypeError:
                        # 如果不支持mapping，逐个设置
                        results = []
                        for field, value in mapping.items():
                            result = await self.client.hset(key, field, value)
                            results.append(result)
                        return sum(results) if results else 0
                else:
                    return await self.client.hset(key, field, value)

        except Exception as e:
            logger.error(f"hset操作失败: {e}")
            raise

    async def hget(self, key: str, field: str):
        """统一的hget接口"""
        return await self.client.hget(key, field)

    async def zrem(self, key: str, member: str):
        """统一的zrem接口"""
        if self.client_type == 'valkey-glide':
            return await self.client.zrem(key, [member])
        elif self.client_type == 'redis-py':
            return await self.client.zrem(key, member)
        else:
            try:
                return await self.client.zrem(key, member)
            except Exception as e:
                logger.error(f"Unknown Redis zrem exception: {e}")

    async def zadd(self, key: str, mapping: Dict[str, float] = None, score: float = None, member: str = None):
        """统一的zadd接口"""
        try:
            if self.client_type == 'redis-py':
                if mapping:
                    # redis-py支持字典格式
                    return await self.client.zadd(key, mapping)
                else:
                    # redis-py也支持score-member方式
                    return await self.client.zadd(key, {member: score})

            elif self.client_type == 'valkey-glide':
                if mapping:
                    # valkey-glide可能也使用字典格式，先尝试
                    try:
                        return await self.client.zadd(key, mapping)
                    except TypeError:
                        # 如果不支持，则逐个添加
                        results = []
                        for member, score in mapping.items():
                            result = await self.client.zadd(key, {member: score})
                            results.append(result)
                        return sum(results) if results else 0
                else:
                    # valkey-glide的单个成员添加
                    return await self.client.zadd(key, {member: score})

            else:
                # 未知客户端，尝试标准方式
                if mapping:
                    try:
                        return await self.client.zadd(key, mapping)
                    except TypeError:
                        # 如果不支持字典格式，逐个添加
                        results = []
                        for member, score in mapping.items():
                            result = await self.client.zadd(key, score, member)
                            results.append(result)
                        return sum(results) if results else 0
                else:
                    return await self.client.zadd(key, {member: score})

        except Exception as e:
            logger.error(f"zadd操作失败: {e}")
            raise

    async def zrangebyscore(self, key: str, min_score: float, max_score: float, withscores: bool = False, ):
        """统一的zrangebyscore接口"""
        try:
            if self.client_type == 'redis-py':
                # redis-py的标准用法
                result_mapping = await self.client.zrangebyscore(key, min_score, max_score, withscores=withscores)
                result = [
                    (_result[0].decode('utf-8') if isinstance(_result[0], bytes) else _result[0], _result[1])
                    for _result in result_mapping
                ]
                return result

            elif self.client_type == 'valkey-glide':
                # valkey-glide使用zrange_withscores方法
                try:
                    # 首先尝试直接的zrangebyscore方法（如果存在）
                    if hasattr(self.client, 'zrangebyscore'):
                        result_mapping = await self.client.zrangebyscore(key, min_score, max_score,
                                                                         withscores=withscores)
                        return result_mapping

                    # 使用zrange_withscores方法
                    elif hasattr(self.client, 'zrange_withscores'):
                        # 需要导入RangeByScore和ScoreBoundary
                        try:
                            # 创建分数范围查询对象
                            range_query = RangeByScore(
                                start=ScoreBoundary(min_score),
                                end=ScoreBoundary(max_score)
                            )

                            # 调用zrange_withscores
                            result_mapping = await self.client.zrange_withscores(key, range_query)

                            logger.debug(f"Result: {result_mapping}")

                            # 转换返回格式以匹配redis-py
                            if withscores:
                                # 返回(member, score)元组列表
                                result = [(member.decode('utf-8') if isinstance(member, bytes) else member, score)
                                          for member, score in result_mapping.items()]
                            else:
                                # 只返回成员列表
                                result = [member.decode('utf-8') if isinstance(member, bytes) else member
                                          for member in result_mapping.keys()]

                            return result

                        except ImportError:
                            logger.error("无法导入valkey-glide的RangeByScore类")
                            raise AttributeError("valkey-glide客户端缺少必要的类")

                    # 尝试使用普通的zrange方法作为备用
                    elif hasattr(self.client, 'zrange'):
                        logger.info("尝试使用zrange作为备用方案")
                        # 注意：这可能不支持按分数范围查询
                        return await self.client.zrange(key, min_score, max_score)

                    else:
                        raise AttributeError("valkey-glide客户端不支持zrangebyscore相关操作")

                except Exception as e:
                    logger.error(f"valkey-glide zrangebyscore操作失败: {e}")
                    raise

            else:
                try:
                    # 未知客户端，尝试标准方式
                    if hasattr(self.client, 'zrangebyscore'):
                        return await self.client.zrangebyscore(key, min_score, max_score, withscores=withscores)
                    elif hasattr(self.client, 'zrange'):
                        return await self.client.zrange(key, min_score, max_score)
                    else:
                        raise AttributeError(f"客户端类型 {self.client_type} 不支持zrangebyscore操作")
                except Exception as e:
                    logger.error(f"Unknown Redis zrangebyscore exception: {e}")

        except Exception as e:
            logger.error(f"zrangebyscore操作失败: {e}")
            raise

    async def expire(self, key: str, time: int):
        """统一的expire接口"""
        return await self.client.expire(key, time)

    async def hgetall(self, key: str):
        """统一的hgetall接口"""
        return await self.client.hgetall(key)

    async def ping(self):
        """统一的ping接口"""
        return await self.client.ping()

    async def aclose(self):
        """统一的关闭接口"""
        if hasattr(self.client, 'aclose'):
            return await self.client.aclose()
        elif hasattr(self.client, 'close'):
            return await self.client.close()
        else:
            logger.warning("客户端没有close方法")


def create_redis_adapter(redis_client) -> RedisAdapter:
    """创建Redis适配器实例"""
    return RedisAdapter(redis_client)
