import redis.asyncio as redis
from app.config import settings
from app.logger import get_logger

from glide import GlideClusterClient, GlideClusterClientConfiguration, NodeAddress

logger = get_logger(__name__)


async def get_client():
    """获取Redis客户端实例（异步版本）"""
    try:
        if settings.ENV == "Local":
            # 构建Redis URL，支持密码
            if settings.REDIS_PASSWORD:
                # 如果设置了密码，重新构建URL
                redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}"
            else:
                redis_url = f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}"

            # 创建异步Redis连接池
            pool = redis.ConnectionPool.from_url(
                redis_url,
                max_connections=50,  # 连接池最大连接数
                retry_on_timeout=True,  # 超时重试
                health_check_interval=30,  # 健康检查间隔
                socket_keepalive=True,  # 启用TCP keepalive
                socket_keepalive_options={},
                encoding="utf-8",
            )

            # 创建异步Redis客户端
            client = redis.Redis(connection_pool=pool)
            # 测试连接
            await client.ping()
            return client
        if settings.ENV == "AWS":
            addresses = [
                NodeAddress("laien-redis-cms-prod-0xnjdd.serverless.use2.cache.amazonaws.com", 6379)]
            config = GlideClusterClientConfiguration(addresses=addresses, use_tls=True)
            client = await GlideClusterClient.create(config)
            return client

        raise Exception(f"Unknown environment: {settings.ENV}")

    except Exception as e:
        logger.error(f"Redis连接失败: {e}")
        # 返回None而不是抛出异常，让调用者处理
        return None


async def close_client(client):
    """彻底关闭Redis客户端连接"""
    if client:
        try:
            # 1. 如果有连接池，先关闭连接池
            if hasattr(client, 'connection_pool') and client.connection_pool:
                try:
                    await client.connection_pool.disconnect()
                except Exception as e:
                    logger.error(f"关闭连接池时出错: {e}")

            # 2. 关闭客户端本身
            if hasattr(client, 'aclose'):
                await client.aclose()
            elif hasattr(client, 'close'):
                await client.close()

        except Exception as e:
            logger.error(f"关闭Redis连接时出错: {e}")
            # 即使出错也要尝试强制关闭
            try:
                if hasattr(client, 'close'):
                    client.close()
            except:
                pass
