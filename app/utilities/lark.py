import json

import httpx
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *
from lark_oapi.api.im.v1 import *

from app.logger import get_logger
from app.config import settings

logger = get_logger(__name__)


class Lark:
    def __init__(self):
        self.client = lark.Client.builder().app_id(settings.APP_ID).app_secret(settings.APP_SECRET).build()

    def batch_create_table_records(self, app_token, table_id, record_data):
        """批量创建数据"""
        create_record = [AppTableRecord.builder().fields(record).build() for record in record_data]
        requests: BatchCreateAppTableRecordRequest = BatchCreateAppTableRecordRequest.builder() \
            .app_token(app_token).table_id(table_id) \
            .user_id_type("user_id") \
            .request_body(BatchCreateAppTableRecordRequestBody.builder().records(create_record).build()) \
            .build()
        response: BatchCreateAppTableRecordResponse = self.client.bitable.v1.app_table_record.batch_create(requests)
        if not response.success():
            logger.error(
                f"self.client.bitable.v1.app_table_record.batch_create_table_record failed, code: {response.code}, "
                f"msg: {response.msg}, log_id: {response.get_log_id()}")
            return
        else:
            str_data = lark.JSON.marshal(response.data)
            return json.loads(str_data)

    def list_app_table(self, app_token):
        """
        列出一个bitable的所有数据表
        """
        request: ListAppTableRequest = ListAppTableRequest.builder() \
            .app_token(app_token) \
            .page_size(100) \
            .build()
        response: ListAppTableResponse = self.client.bitable.v1.app_table.list(request)
        if not response.success():
            logger.error(
                f"client.bitable.v1.app_table.list failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return {}

        response_data = lark.JSON.marshal(response.data, indent=4)
        return json.loads(response_data)

    def get_tenant_access_token(self):
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/"

        payload = {
            "app_id": settings.APP_ID,
            "app_secret": settings.APP_SECRET
        }

        r = httpx.post(url, json=payload)
        if r:
            token = r.json().get('tenant_access_token')
            return token

    def create_app_table(self, app_token, table_name, tabel_fields: list):
        token = self.get_tenant_access_token()
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables"
        headers = {"Authorization": f"Bearer {token}"}
        payload = {
            "table": {
                "name": table_name,
                "fields": tabel_fields
            }
        }
        retry_num = 0
        while retry_num < 3:
            try:
                r = httpx.post(url, headers=headers, json=payload)
                r.raise_for_status()
                table_id = r.json()["data"]["table_id"]
                return table_id
            except Exception as e:
                logger.warning(f"创建数据表 {table_name} 失败, 开始第 {retry_num + 1} 次重试, Exception: {e}")
                retry_num += 1

        if retry_num == 3:
            logger.error(f"3次创建 {table_name} 均失败")
        return None

    def get_table_records_number(self, app_token, table_id):
        request: SearchAppTableRecordRequest = SearchAppTableRecordRequest().builder() \
            .app_token(app_token).table_id(table_id) \
            .request_body(SearchAppTableRecordRequestBody.builder().build()).build()

        response: SearchAppTableRecordResponse = self.client.bitable.v1.app_table_record.search(request)

        if not response.success():
            logger.error(
                f"self.client.bitable.v1.app_table_record.search failed, code: {response.code},"
                f"msg: {response.msg}, log_id: {response.get_log_id()}"
            )
        else:
            str_data = lark.JSON.marshal(response.data)
            data = json.loads(str_data)
            record_num = data["total"]
            return record_num

    def post_message(self, receive_id_type, receive_id, msg_type, content):
        request: CreateMessageRequest = CreateMessageRequest.builder() \
            .receive_id_type(receive_id_type) \
            .request_body(CreateMessageRequestBody.builder()
                          .receive_id(receive_id)
                          .msg_type(msg_type)
                          .content(content)
                          .build()
                          ).build()

        response: CreateMessageResponse = self.client.im.v1.message.create(request)

        if not response.success():
            logger.error(f"client.im.v1.message.create failed, "
                         f"code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return False
        else:
            logger.info("client.im.v1.message.create success")
            return True
