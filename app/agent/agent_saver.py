import time
import json
from typing import List, Dict, Any, Optional

import orjson
from llama_index.core.memory import Memory
from llama_index.core.llms import ChatMessage

from app.logger import get_logger
from app.config import settings
from app.redis_server.redis_client import get_client, close_client
from app.redis_server.redis_adapter import create_redis_adapter, RedisAdapter

logger = get_logger(__name__)


class AgentSaver:
    """
    用于保存和加载Agent状态的工具类
    1. 从Redis加载历史对话
    2. 将Memory中的消息保存到Redis
    3. 删除Redis中的历史对话
    4. 从Redis加载Context
    5. 将Context保存到Redis
    6. 删除Redis中的Context
    """

    def __init__(self, chat_id: str, ttl: int = settings.REDIS_TTL):
        self.chat_id = chat_id
        self.redis_memory_key = f"{settings.KEY_TITLE}-Memory-{self.chat_id}"
        self.redis_ctx_key = f"{settings.KEY_TITLE}-context-{chat_id}"
        self.redis_ttl = ttl
        self.timestamp_index_key = f"{settings.KEY_TITLE}-timestamp-index"
        self.redis_client: Optional[RedisAdapter] = None

    async def get_redis_client(self):
        """创建Redis连接"""
        if self.redis_client is None:
            raw_client = await get_client()
            if raw_client is None:
                logger.error("无法获取Redis客户端")
                raise ConnectionError("Redis客户端连接失败")
            self.redis_client = create_redis_adapter(raw_client)
        return self.redis_client

    async def close_redis_connection(self):
        """关闭Redis连接"""
        if self.redis_client:
            if hasattr(self.redis_client, "client"):
                # 如果是适配器，获取底层客户端
                raw_client = self.redis_client.client
                await close_client(raw_client)
            self.redis_client = None

    async def init_memory(self) -> Memory:
        """初始化Memory实例并从Redis加载历史对话"""
        # 创建Memory实例
        memory = Memory.from_defaults(
            token_limit=40000,
            session_id=self.redis_memory_key,
        )

        # 从Redis加载历史对话，使用新的Hash格式
        chat_history = await self.get_chat_history()
        if chat_history:
            # 将历史消息添加到Memory中
            memory.put_messages(chat_history)
            logger.info(f"从Redis加载了 {len(chat_history)} 条历史消息")

        return memory

    async def get_chat_history(self) -> List[ChatMessage]:
        """从Redis Hash获取聊天历史，返回List[ChatMessage]，支持旧格式兼容"""
        try:

            # 首先尝试从新的Hash格式获取数据
            data = await self.redis_client.hget(self.redis_memory_key, "data")
            if data:
                # 解析JSON数据
                messages_dict = json.loads(data)
                messages_list = messages_dict.get("messages", [])

                # 转换回ChatMessage对象
                chat_messages = [
                    self._dict_to_message(msg_dict) for msg_dict in messages_list
                ]

                logger.debug(f"从Redis Hash加载了 {len(chat_messages)} 条历史消息")
                return chat_messages
            else:
                logger.debug(f"Redis中不存在聊天历史: {self.redis_memory_key}")
                return []

        except Exception as e:
            logger.error(f"获取聊天历史失败: {e}")
            return []

    def _dict_to_message(self, msg_dict: Dict[str, Any]) -> ChatMessage:
        """将字典转换为ChatMessage"""
        return ChatMessage(
            role=msg_dict.get("role", "user"),
            content=msg_dict.get("content", ""),
            additional_kwargs=msg_dict.get("additional_kwargs", {}),
        )

    async def save_memory_to_redis(self, memory: Memory, sub_id: str, source: str):
        """将Memory中的消息保存到Redis Hash，并更新时间戳索引"""
        # 获取当前Memory中的所有消息
        current_messages = await memory.aget_all()
        if not current_messages:
            logger.debug("Memory中没有消息，无需保存")
            return

        # 将消息列表转换为dict格式
        current_timestamp_ms = int(time.time() * 1000)
        messages_dict = {
            "messages": [self._message_to_dict(msg) for msg in current_messages],
            "last_updated": current_timestamp_ms,
            "chat_id": self.chat_id,
            "sub_id": sub_id,
            "source": source
        }

        # 使用适配器统一处理不同Redis客户端
        await self.redis_client.hset(
            self.redis_memory_key,
            mapping={
                "data": json.dumps(messages_dict),
                "last_updated": str(current_timestamp_ms),
            },
        )

        # 更新时间戳索引
        await self._update_timestamp_index()

        logger.info(
            f"保存了 {len(current_messages)} 条消息到Redis Hash并更新了时间戳索引"
        )

    def _message_to_dict(self, message: ChatMessage) -> Dict[str, Any]:
        """将ChatMessage转换为字典"""
        return {
            "role": message.role,
            "content": message.content,
            "additional_kwargs": message.additional_kwargs,
        }

    async def _update_timestamp_index(self):
        """更新时间戳索引"""
        current_timestamp_ms = int(time.time() * 1000)

        # 使用适配器统一处理不同Redis客户端
        await self.redis_client.zadd(
            self.timestamp_index_key,
            mapping={self.redis_memory_key: current_timestamp_ms},
        )

    async def delete_chat_history(self):
        """删除聊天历史和时间戳索引"""

        # 删除新格式的Hash数据
        await self.redis_client.delete(self.redis_memory_key)
        # 同时从时间戳索引中移除
        await self.redis_client.zrem(self.timestamp_index_key, self.redis_memory_key)
        logger.info(f"删除了聊天历史: {self.redis_memory_key}")

    async def get_user_context(self) -> Dict[str, Any]:
        """
        从 Redis 中获取指定用户的Context。
        """
        context_str = await self.redis_client.get(self.redis_ctx_key)
        if context_str:
            context_dict = orjson.loads(context_str)
            logger.debug(f"成功从Redis加载对话: {self.chat_id} 的Context")
            return context_dict
        else:
            logger.debug(f"Redis中不存在对话: {self.chat_id} 的Context")
            return {}

    async def set_user_context(self, context_dict: Dict[str, Any], chat_id: str):
        """
        修改 Redis 中指定对话的Context。
        """
        if context_dict:
            # 将context作为独立的Redis键存储，并设置TTL
            await self.redis_client.set(
                self.redis_ctx_key, orjson.dumps(context_dict), ex=self.redis_ttl
            )
            logger.debug(f"设置对话 {chat_id} 的Context，TTL: {self.redis_ttl}秒")

    async def delete_user_context(self, chat_id: str) -> int:
        """
        删除 Redis 中指定对话的Context。
        返回是否成功删除（int）
            1: 成功删除
            0: 删除失败(没有对应的context_key)
        """
        result = await self.redis_client.delete(self.redis_ctx_key)
        if result:
            logger.info(f"成功删除对话 {chat_id} 的Context")
        return result
