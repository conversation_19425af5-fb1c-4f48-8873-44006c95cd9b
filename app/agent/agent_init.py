from typing import Optional
from llama_index.core.agent.workflow import FunctionAgent, AgentWorkflow
from llama_index.core.tools import FunctionTool

from app.agent import agent_llms
from app.agent.version_manager.versioned_tools import ToolManager
from app.agent.version_manager.version_manager import APIVersion
from app.config import settings


class AgentInit:
    def __init__(self, user_language: str = "zh", version: Optional[APIVersion] = None):
        self.user_language = user_language
        self.tools = ToolManager.get_tools(version=version, language=user_language)

    def init_agent(self) -> AgentWorkflow:
        # 获取system prompt
        system_prompt = self.tools.get_system_prompt()

        # 定义修改workout plan的工具
        adjust_plan_tool = FunctionTool.from_defaults(
            fn=self.tools.adjust_plan,
            name="adjust_workout_plan_tool",
            description="修改用户的健身计划，可一次修改多个选项包括运动类型、限制部位、器械、时长、教练性别和姿势偏好",
        )

        # 定义获取用户workout plan信息的工具
        get_user_workout_plan_tool = FunctionTool.from_defaults(
            fn=self.tools.get_user_workout_plan,
            name="get_user_workout_plan_tool",
            description="获取用户当前的运动计划信息，包括运动类型、限制部位、时长、器械、教练性别和姿势偏好",
        )

        # 构造获取指定服务的工具
        get_serve_rules = FunctionTool.from_defaults(
            fn=self.tools.get_serve_rule,
            name="get_serve_rule_tool",
            description="获取指定服务的规则(Rule)"
        )

        # 获取修改workout规则的工具
        get_update_workout_rule_tool = FunctionTool.from_defaults(
            fn=self.tools.get_update_workout_rule,
            name="get_update_workout_rule_tool",
            description="获取修改运动计划多样性、强度的规则(Rule)"
        )

        llm = agent_llms.openai_llm()
        agent = FunctionAgent(
            llm=llm,
            system_prompt=system_prompt,
            tools=[adjust_plan_tool, get_user_workout_plan_tool, get_serve_rules, get_update_workout_rule_tool]
        )
        agent_workflow = AgentWorkflow(
            agents=[agent],
            initial_state={},
            timeout=settings.AGENT_TIMEOUT,
            verbose=settings.DEBUG
        )

        return agent_workflow
