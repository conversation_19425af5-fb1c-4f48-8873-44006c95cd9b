from typing import Dict, Any, Optional
from enum import Enum


class VersionError(Exception):
    pass


class APIVersion(Enum):
    """API版本枚举"""
    V1 = "v1"
    V2 = "v2"

    @classmethod
    def from_string(cls, version_str: str) -> 'APIVersion':
        """从字符串创建版本枚举"""
        version_map = {
            "v1": cls.V1,
            "v2": cls.V2,
            "1": cls.V1,
            "2": cls.V2,
        }
        return version_map.get(version_str.lower(), cls.V1)

    def __str__(self) -> str:
        return self.value
