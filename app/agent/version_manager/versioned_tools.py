from typing import List, Annotated, Dict, Any

import httpx
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
from llama_index.core.workflow import Context

from app.agent.version_manager.versioned_schema import SchemaManager
from app.agent.version_manager.version_manager import APIVersion, VersionError
from app.agent.version_manager.versioned_mappings import MappingsManager
from app.agent.version_manager.versioned_prompt import AgentPromptManager
from app.logger import get_logger
from app.config import settings

logger = get_logger(__name__)


class MissingValueError(Exception):
    """缺失值异常"""
    pass


class WrongValueError(Exception):
    """错误值异常"""
    pass


v1_schema = SchemaManager.get_schema(version=APIVersion.V1)
v2_schema = SchemaManager.get_schema(version=APIVersion.V2)


class ToolsFactory:
    """Agent的通用工具"""

    def __init__(self, version: APIVersion):
        self.version = version
        self.mapping = MappingsManager.get_mappings(version=version)

    def get_str_mapping_data(self, key: str, value: str, mapping=None) -> int:
        """
        根据键值对获取字符串映射数据，将字符串值转换为对应的整数代码

        Args:
            key (str): 映射字典的键名
            value (str): 需要映射的字符串值
            mapping (dict, optional): 映射字典，默认使用 workout_reverse_mapping

        Returns:
            int: 映射后的整数值

        Raises:
            Exception: 当映射过程中发生错误时抛出异常
        """
        if mapping is None:
            mapping = self.mapping.workout_reverse_mapping
        mapping_dict = mapping[key]
        mapping_value = mapping_dict[value]
        return mapping_value

    def get_list_mapping_data(self, key: str, value: List[str], mapping=None) -> List:
        """
        根据键值对获取列表映射数据，将字符串列表转换为对应的整数代码列表

        Args:
            key (str): 映射字典的键名
            value (List[str]): 需要映射的字符串列表
            mapping (dict, optional): 映射字典，默认使用 workout_reverse_mapping

        Returns:
            List: 映射后的整数列表

        Raises:
            Exception: 当映射过程中发生错误时抛出异常
        """
        if mapping is None:
            mapping = self.mapping.workout_reverse_mapping
        mapping_list = []
        mapping_dict = mapping[key]

        # workout_type
        if "All workout type" in value:
            mapping_list = [10, 11, 12, 13, 14, 17]
        # physical_limitation
        elif "I'm all good" in value:
            mapping_list = []
        # equipment
        elif "No Equipment" in value:
            mapping_list = [10]
        else:
            for data in value:
                mapping_list.append(mapping_dict[data])

        return mapping_list

    def mapping_int_to_str(self, workout_plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        将运动计划模型中的整数代码转换为对应的字符串值

        Args:
            workout_plan (Dict[str, Any]): 包含整数代码的运动计划模型

        Returns:
            Dict[str, Any]: 转换后包含字符串值的运动计划字典

        Raises:
            Exception: 当映射过程中发生错误时抛出异常
        """
        try:
            for key, value in workout_plan.items():
                # if key in key_reverse_mapping:
                if key in self.mapping.key_reverse_mapping:
                    if key in settings.STR_KEY_LIST and value:
                        mapping_value = self.get_str_mapping_data(key, value, self.mapping.workout_mapping)
                        workout_plan[key] = mapping_value
                    if key in settings.LIST_KEY_LIST:
                        mapping_value = self.get_list_mapping_data(key, value, self.mapping.workout_mapping)
                        workout_plan[key] = mapping_value

            return workout_plan
        except Exception as e:
            logger.error(
                f"整数到字符串映射失败，workout_plan: {workout_plan}, key: {key}, value: {value} error: {e}"
            )
            raise Exception(f"映射workout plan失败: {e}") from e

    def mapping_str_to_int(self, user_workout_plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        将用户运动计划中的字符串值映射为整数代码

        Args:
            user_workout_plan (Dict[str, Any]): 包含字符串值的用户运动计划字典

        Returns:
            Dict[str, Any]: 映射后包含整数代码的运动计划字典

        Raises:
            Exception: 当映射过程中发生错误时抛出异常
        """
        try:
            for key, value in user_workout_plan.items():
                if key in settings.STR_KEY_LIST and value:
                    user_workout_plan[key] = self.get_str_mapping_data(key, value)
                if key in settings.LIST_KEY_LIST and value:
                    user_workout_plan[key] = self.get_list_mapping_data(key, value)
            return user_workout_plan
        except Exception as e:
            logger.error(
                f"字符串到整数映射失败，user_workout_plan: {user_workout_plan}, key: {key}, value: {value} error: {e}"
            )
            raise Exception(f"字符串到整数映射失败: {e}") from e

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=4),
        retry=retry_if_exception_type(),
    )
    def update_workout_plan(self, params):
        """
        调用外部API更新用户的运动计划

        Args:
            params (dict): 包含运动计划参数的字典

        Returns:
            dict: API响应数据

        Raises:
            Exception: 当API调用失败或返回错误代码时抛出异常
        """
        try:
            url = settings.URL_MAP[self.version.value]
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
            }

            response = httpx.get(url, headers=headers, params=params)
            response.raise_for_status()
            response_data = response.json()
            response_code = response_data["code"]
            logger.debug(f"Response code: {response_code}")
            if response_code != 200:
                raise Exception(f"调用修改Workout Plan接口失败, Code: {response_code}")

            return response_data
        except httpx.HTTPError as http_error:
            logger.error(f"修改workout plan接口请求失败, Error: {http_error}")
            raise httpx.HTTPError(f"更新运动计划失败: {http_error}")
        except Exception as e:
            logger.error(f"更新运动计划失败，params: {params}, error: {e}")
            raise Exception(f"更新运动计划失败: {e}")


class V1AgentTool(ToolsFactory):
    """V1版本的Function Call Tool"""

    def __init__(self, version: APIVersion, language: str = "en"):
        super().__init__(version)
        self.version = version
        self.language = language
        self.prompt_manager = AgentPromptManager.get_rules(version=version, language=language)

    async def adjust_plan(
            self,
            ctx: Context,
            exerciseTypeCodeSet: Annotated[List[str], v1_schema.workout_type],
            restrictionCodeSet: Annotated[List[str], v1_schema.physical_limitation],
            equipmentCodeSet: Annotated[List[str], v1_schema.equipment],
            durationCode: Annotated[str, v1_schema.workout_duration],
            coachGenderCode: Annotated[str, v1_schema.coach_gender],
            positionCode: Annotated[str, v1_schema.preferred_position],
    ) -> str:
        """
        修改用户的健身计划，可一次修改多个选项

        Returns:
            str: 操作结果描述，包含成功更新的字段或错误信息

        Raises:
            Exception: 当提供的参数值不在允许的范围内时
            MissingValueError: 用户的exerciseTypeCodeSet选择少于2个，礼貌要求用户增加
            WrongValueError: 用户选择的positionCode和exerciseTypeCodeSet冲突
        """
        try:
            # 兜底判断 exerciseTypeCodeSet 参数数量，小于2抛错
            if len(exerciseTypeCodeSet) < 2:
                raise MissingValueError("exerciseTypeCodeSet选择少于2个，礼貌要求用户增加")

            # 兜底判断 chair yoga 与 position的关联
            if "Standing" in positionCode and "Chair yoga" in exerciseTypeCodeSet:
                raise WrongValueError(
                    "选择Chair yoga时必须选择坐姿或者站姿+坐姿，礼貌要求用户修改"
                )

            # 兜底判断Tai chi与position的关联
            if "Tai chi" in exerciseTypeCodeSet and "Seated on chair" in positionCode:
                raise WrongValueError(
                    "选择Tai chi时必须选择站姿或者站姿+坐姿，礼貌要求用户修改"
                )

            # 兜底Gentle cardio与position的关联
            if "Gentle cardio" in exerciseTypeCodeSet and "Seated on chair" in positionCode:
                raise WrongValueError(
                    "选择Gentle cardio时必须选择站姿或者站姿+坐姿，礼貌要求用户修改"
                )

            # 需要修改的参数
            plan_list = [
                {"key": "exerciseTypeCodeSet", "value": exerciseTypeCodeSet},
                {"key": "durationCode", "value": durationCode},
                {"key": "positionCode", "value": positionCode},
                {"key": "equipmentCodeSet", "value": equipmentCodeSet},
                {"key": "coachGenderCode", "value": coachGenderCode},
                {"key": "restrictionCodeSet", "value": restrictionCodeSet},
            ]
            logger.info(f"V1版本: 调用 adjust plan tool，参数：{plan_list}")

            # 根据映射关系，将用户需要修改的参数，映射为接口参数
            params = await ctx.store.get("workout_mapping_data", {})
            logger.info(f"V1版本: User Workout Plan: {params}")

            modify_params = {}

            # 对 equipmentCodeSet 和 restrictionCodeSet做单独处理
            equipmentCodeSet = [10] if equipmentCodeSet == [] else equipmentCodeSet
            params["equipmentCodeSet"] = equipmentCodeSet
            modify_params["equipmentCodeSet"] = ["No Equipment"]

            params["restrictionCodeSet"] = restrictionCodeSet
            modify_params["restrictionCodeSet"] = []
            logger.info(f"V1版本: 单独处理后的User Workout Plan: {params}")

            for plan in plan_list:
                key = plan["key"]
                value = plan["value"]

                if key in settings.STR_KEY_LIST and value:
                    mapping_value = self.get_str_mapping_data(key, value)
                    params[key] = mapping_value
                    modify_params[key] = value

                if key in settings.LIST_KEY_LIST and value:
                    mapping_value = self.get_list_mapping_data(key, value)
                    params[key] = mapping_value
                    modify_params[key] = value

            # 根据positionCode，修改workout type
            position = params["positionCode"]
            workout_type = params["exerciseTypeCodeSet"]
            # 用户选择了站姿，不提供chair yoga(17)
            if 10 == position:
                if 17 in workout_type:
                    workout_type.remove(17)
            # 用户选择了坐姿，不提供Gentle cardio(13)和Tai chi(11)
            elif 11 == position:
                for exercise in [11, 13]:
                    if exercise in workout_type:
                        workout_type.remove(exercise)
            params["exerciseTypeCodeSet"] = workout_type

            # 判断运动器械是否有压力和弹力带
            equipment = params["equipmentCodeSet"]
            workout_type = params["exerciseTypeCodeSet"]
            # 用户运动器械中包含哑铃，在workout type中添加对应参数 15
            if 11 in equipment:
                logger.debug("V1版本: 用户选择哑铃，更新workout type")
                workout_type.append(15)
            # 用户运动器械中包含弹力带，在workout type中添加对应参数 16
            if 12 in equipment:
                logger.debug("V1版本: 用户选择弹力带，更新workout type")
                workout_type.append(16)
            params["exerciseTypeCodeSet"] = workout_type

            # 添加用户语言
            params["lang"] = self.language
            logger.info(f"V1版本: 调用接口的Params: {params}")

            update_result = self.update_workout_plan(params)

            response_code = update_result["code"]
            response_message = update_result["message"]
            response_data = update_result["data"]

            await ctx.store.set("update_workout_data", response_data)
            await ctx.store.set("update_workout_message", response_message)
            await ctx.store.set("update_workout_code", response_code)
            await ctx.store.set("refresh_workout_plan", True)
            await ctx.store.set("workout_updated_mapping_data", params)

            logger.info("V1版本: 修改Workout Plan成功")

            return f"修改用户的workout plan成功，修改参数为: {modify_params}"

        except MissingValueError as value_error:
            logger.error(f"修改运动计划失败，原因：{value_error}")
            return str(value_error)

        except Exception as e:
            error_msg = f"修改运动计划失败，原因： {str(e)}"
            logger.error(error_msg)
            return error_msg

    async def get_user_workout_plan(self, ctx: Context):
        """
        获取用户当前的运动计划信息

        Returns:
            dict: 用户当前的运动计划信息

            Example:
                {
                "workout_type": ["Chair yoga", "Tai chi"],  # 用户现在的workout type
                "physical_limitation": ["Shoulder", "Back"],  # 用户现在避免锻炼的部位，如果为[]，则意味用户没有需要避免运动的部位
                "workout_duration": "5-10 minutes",  # 用户现在的运动时长
                "equipment": "No Equipment",  # 用户现在使用的运动器械
                "coach": "Mixed Coaches",  # 用户现在选择的教练性别
                "preferred_position": "All position"  # 用户现在选择的运动姿势
            }

        Raises:
            ValueError: 从Context中获取用户的运动计划失败的信息
        """
        chat_id = await ctx.store.get("chat_id")
        try:
            user_workout_plan = await ctx.store.get("workout_mapping_data", {})
            if not user_workout_plan:
                raise ValueError(f"Workout plan not found for chat_id: {chat_id}")
            return user_workout_plan
        except Exception as e:
            logger.error(f"获取 {chat_id} 的Workout Plan失败, Error: {e}")
            return ValueError(str(e))

    def get_system_prompt(self):
        return self.get_system_prompt()

    def get_serve_rule(
            self,
            serve: Annotated[int, """各服务规则对应的code
            0: 修改Workout Plan服务 规则,
            1: Subscription&Billing服务 规则,
            2: Refit App 使用引导服务 规则,
            3: Refit App Bug类问题服务 规则,
            4: Health Advice服务 规则
            """]
    ):
        """
        根据传入的服务类型Code，获取对应的服务规则

        Returns:
            str: serve对应服务的rule

        Raises:
            WrongValueError: 传入的serve code不支持
            MissingValueError: 没有serve对应的rule
        """
        if serve not in [0, 1, 2, 3, 4]:
            raise WrongValueError(f"Serve {serve} is not supported")
        rule = ""
        match serve:
            case 0:
                rule = self.prompt_manager.get_update_workout_rule()
            case 1:
                rule = self.prompt_manager.get_sub_and_billing_rule()
            case 2:
                rule = self.prompt_manager.get_use_guide_rule()
            case 3:
                rule = self.prompt_manager.get_troubleshooting_rule()
            case 4:
                rule = self.prompt_manager.get_health_advice_rule()

        if not rule:
            raise MissingValueError(f"Get Serve: {serve} Rule Failed")
        return rule

    def get_update_workout_rule(
            self,
            serve_num: Annotated[int, """需要获取的用户计划调整规则
            0: 调整运动计划多样性规则
            1: 调整用户运动计划强度-易改难的规则
            2: 调整用户运动计划强度-难改易读规则
            """]
    ):
        """
        根据传入的服务类型Code，获取对应的运动计划修改规则


        Returns:
            str: serve对应服务运动计划修改规则的

        Raises:
            WrongValueError: 传入的serve code不支持
            MissingValueError: 没有serve对应的rule
        """
        if serve_num not in [0, 1, 2]:
            raise WrongValueError(f"Serve {serve_num} is not supported")

        rule = ""
        match serve_num:
            case 0:
                rule = self.prompt_manager.get_adjust_diversity_rule()
            case 1:
                rule = self.prompt_manager.get_easy_2_hard_rule()
            case 2:
                rule = self.prompt_manager.get_hard_2_easy_rule()

        if not rule:
            raise MissingValueError(f"Get Serve: {serve_num} Rule Failed")
        return rule


class V2AgentTool(V1AgentTool):
    """V2版本的Function Call Tool"""

    def __init__(self, version: APIVersion, language: str = "en"):
        super().__init__(version=version, language=language)
        self.version = version

    async def adjust_plan(
            self,
            ctx: Context,
            exerciseTypeCodeSet: Annotated[List[str], v2_schema.workout_type],
            restrictionCodeSet: Annotated[List[str], v2_schema.physical_limitation],
            equipmentCodeSet: Annotated[List[str], v2_schema.equipment],
            durationCode: Annotated[str, v2_schema.workout_duration],
            coachGenderCode: Annotated[str, v2_schema.coach_gender],
            positionCode: Annotated[str, v2_schema.preferred_position],
    ) -> str:
        """
        修改用户的健身计划，可一次修改多个选项

        Returns:
            str: 操作结果描述，包含成功更新的字段或错误信息

        Raises:
            Exception: 当提供的参数值不在允许的范围内时
            MissingValueError: 用户的exerciseTypeCodeSet选择少于2个，礼貌要求用户增加
            WrongValueError: 用户选择的positionCode和exerciseTypeCodeSet冲突
        """
        try:
            # 兜底判断 exerciseTypeCodeSet 参数数量，小于2抛错
            if len(exerciseTypeCodeSet) < 2:
                raise MissingValueError("exerciseTypeCodeSet选择少于2个，礼貌要求用户增加")

            # 兜底判断 chair yoga 与 position的关联
            if "Standing" in positionCode and "Chair yoga" in exerciseTypeCodeSet:
                raise WrongValueError(
                    "选择Chair yoga时必须选择坐姿或者站姿+坐姿，礼貌要求用户修改"
                )

            # 兜底判断Tai chi与position的关联
            if "Tai chi" in exerciseTypeCodeSet and "Seated on chair" in positionCode:
                raise WrongValueError(
                    "选择Tai chi时必须选择站姿或者站姿+坐姿，礼貌要求用户修改"
                )

            # 兜底Gentle cardio与position的关联
            if "Gentle cardio" in exerciseTypeCodeSet and "Seated on chair" in positionCode:
                raise WrongValueError(
                    "选择Gentle cardio时必须选择站姿或者站姿+坐姿，礼貌要求用户修改"
                )

            # 需要修改的参数
            plan_list = [
                {"key": "exerciseTypeCodeSet", "value": exerciseTypeCodeSet},
                {"key": "durationCode", "value": durationCode},
                {"key": "positionCode", "value": positionCode},
                {"key": "equipmentCodeSet", "value": equipmentCodeSet},
                {"key": "coachGenderCode", "value": coachGenderCode},
                {"key": "restrictionCodeSet", "value": restrictionCodeSet},
            ]
            logger.info(f"V2版本: 调用 adjust plan tool，参数：{plan_list}")

            # 根据映射关系，将用户需要修改的参数，映射为接口参数
            params = await ctx.store.get("workout_mapping_data", {})
            logger.info(f"V2版本: User Workout Plan: {params}")

            modify_params = {}

            # 对 equipmentCodeSet 和 restrictionCodeSet做单独处理
            equipmentCodeSet = [10] if equipmentCodeSet == [] else equipmentCodeSet
            params["equipmentCodeSet"] = equipmentCodeSet
            modify_params["equipmentCodeSet"] = ["No Equipment"]

            params["restrictionCodeSet"] = restrictionCodeSet
            modify_params["restrictionCodeSet"] = []
            logger.info(f"V2版本: 单独处理后的User Workout Plan: {params}")

            for plan in plan_list:
                key = plan["key"]
                value = plan["value"]

                if key in settings.STR_KEY_LIST and value:
                    mapping_value = self.get_str_mapping_data(key, value)
                    params[key] = mapping_value
                    modify_params[key] = value

                if key in settings.LIST_KEY_LIST and value:
                    mapping_value = self.get_list_mapping_data(key, value)
                    params[key] = mapping_value
                    modify_params[key] = value

            # 根据positionCode，修改workout type
            position = params["positionCode"]
            workout_type = params["exerciseTypeCodeSet"]
            # 用户选择了站姿，不提供chair yoga(17)
            if 10 == position:
                if 17 in workout_type:
                    workout_type.remove(17)
            # 用户选择了坐姿，不提供Gentle cardio(13)和Tai chi(11)
            elif 11 == position:
                for exercise in [11, 13]:
                    if exercise in workout_type:
                        workout_type.remove(exercise)
            params["exerciseTypeCodeSet"] = workout_type

            # 判断运动器械是否有压力和弹力带
            equipment = params["equipmentCodeSet"]
            workout_type = params["exerciseTypeCodeSet"]
            # 用户运动器械中包含哑铃，在workout type中添加对应参数 15
            if 11 in equipment:
                logger.debug("V2版本: 用户选择2-6磅哑铃，更新workout type")
                workout_type.append(15)
            # 用户运动器械中包含弹力带，在workout type中添加对应参数 16
            if 12 in equipment:
                logger.debug("V2版本: 用户选择弹力带，更新workout type")
                workout_type.append(16)
            if 15 in equipment:
                logger.debug("V2版本: 用户选择6-10磅哑铃，更新workout type")
                workout_type.append(21)
            params["exerciseTypeCodeSet"] = workout_type

            # 添加用户语言
            params["lang"] = self.language
            logger.info(f"V2版本: 调用接口的Params: {params}")

            update_result = self.update_workout_plan(params)

            response_code = update_result["code"]
            response_message = update_result["message"]
            response_data = update_result["data"]

            await ctx.store.set("update_workout_data", response_data)
            await ctx.store.set("update_workout_message", response_message)
            await ctx.store.set("update_workout_code", response_code)
            await ctx.store.set("refresh_workout_plan", True)
            await ctx.store.set("workout_updated_mapping_data", params)

            logger.info("V2版本: 修改Workout Plan成功")

            return f"修改用户的workout plan成功，修改参数为: {modify_params}"

        except MissingValueError as value_error:
            logger.error(f"修改运动计划失败，原因：{value_error}")
            return str(value_error)

        except Exception as e:
            error_msg = f"修改运动计划失败，原因： {str(e)}"
            logger.error(error_msg)
            return error_msg


class ToolManager:
    @classmethod
    def get_tools(cls, version: APIVersion, language: str = "en"):
        if version == APIVersion.V1:
            tools = V1AgentTool(version=version, language=language)
        elif version == APIVersion.V2:
            tools = V2AgentTool(version=version, language=language)
        else:
            raise VersionError(f"Version {version} not supported")

        return tools
