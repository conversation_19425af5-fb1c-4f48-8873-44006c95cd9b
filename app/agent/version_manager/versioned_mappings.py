from typing import Dict, List

from app.logger import get_logger
from app.agent.version_manager.version_manager import APIVersion, VersionError

logger = get_logger(__name__)


class V1Mappings:
    """
    V1映射系统
    """

    # 教练性别映射
    coach_gender_mapping: Dict[int, str] = {10: "Female", 11: "Male", 12: "Mixed Coaches"}
    coach_gender_reverse_mapping: Dict[str, int] = {
        v: k for k, v in coach_gender_mapping.items()
    }

    # 时长标签映射
    duration_mapping: Dict[int, str] = {
        10: "5-10 minutes",
        11: "10-15 minutes",
        12: "15-20 minutes",
        13: "20-30 minutes",
    }
    duration_reverse_mapping: Dict[str, int] = {v: k for k, v in duration_mapping.items()}

    # 器械映射
    equipment_mapping: Dict[int, str] = {
        10: "No Equipment",
        11: "Dumbbell",
        12: "Resistance Band",
    }
    equipment_reverse_mapping: Dict[str, int] = {v: k for k, v in equipment_mapping.items()}

    # 运动类型映射
    workout_type_mapping: Dict[int, str] = {
        10: "Chair cardio",
        11: "Tai chi",  # 站姿专属
        12: "Dancing",
        13: "Gentle cardio",  # 站姿专属
        14: "Indoor walking",
        17: "Chair yoga",  # 坐姿专属
    }
    workout_type_reverse_mapping: Dict[str, int] = {
        v: k for k, v in workout_type_mapping.items()
    }

    # 姿势映射
    position_mapping: Dict[int, str] = {
        10: "Standing",
        11: "Seated on chair",
        12: "All position",
    }
    position_reverse_mapping: Dict[str, int] = {v: k for k, v in position_mapping.items()}

    # 限制部位映射
    restriction_mapping: Dict[int, str] = {
        10: "Shoulder",
        11: "Back",
        12: "Wrist",
        13: "Knee",
        14: "Ankle",
        15: "Hip",
    }
    restriction_reverse_mapping: Dict[str, int] = {
        v: k for k, v in restriction_mapping.items()
    }

    workout_mapping: Dict[str, Dict[int, str]] = {
        "exerciseTypeCodeSet": workout_type_mapping,
        "restrictionCodeSet": restriction_mapping,
        "durationCode": duration_mapping,
        "equipmentCodeSet": equipment_mapping,
        "coachGenderCode": coach_gender_mapping,
        "positionCode": position_mapping,
    }
    workout_reverse_mapping: Dict[str, Dict[str, int]] = {
        "exerciseTypeCodeSet": workout_type_reverse_mapping,
        "restrictionCodeSet": restriction_reverse_mapping,
        "durationCode": duration_reverse_mapping,
        "equipmentCodeSet": equipment_reverse_mapping,
        "coachGenderCode": coach_gender_reverse_mapping,
        "positionCode": position_reverse_mapping,
    }

    # key的映射
    key_mapping: Dict[str, str] = {
        "workout_type": "exerciseTypeCodeSet",
        "equipment": "equipmentCodeSet",
        "physical_limitation": "restrictionCodeSet",
        "workout_duration": "durationCode",
        "coach_gender": "coachGenderCode",
        "preferred_position": "positionCode",
    }
    key_reverse_mapping: Dict[str, str] = {v: k for k, v in key_mapping.items()}


class V2Mappings:
    """
    V1映射系统
    """

    # 教练性别映射
    coach_gender_mapping: Dict[int, str] = {10: "Female", 11: "Male", 12: "Mixed Coaches"}
    coach_gender_reverse_mapping: Dict[str, int] = {
        v: k for k, v in coach_gender_mapping.items()
    }

    # 时长标签映射
    duration_mapping: Dict[int, str] = {
        10: "5-10 minutes",
        11: "10-15 minutes",
        12: "15-20 minutes",
        13: "20-30 minutes",
    }
    duration_reverse_mapping: Dict[str, int] = {v: k for k, v in duration_mapping.items()}

    # 器械映射
    equipment_mapping: Dict[int, str] = {
        10: "No Equipment",
        11: "2-6 lbs Dumbbell",
        12: "Resistance Band",
        15: "6-10 lbs Dumbbell",
    }
    equipment_reverse_mapping: Dict[str, int] = {v: k for k, v in equipment_mapping.items()}

    # 运动类型映射
    workout_type_mapping: Dict[int, str] = {
        10: "Chair cardio", # 坐姿专属
        11: "Tai chi",  # 站姿专属
        12: "Dancing",
        13: "Gentle cardio",  # 站姿专属
        14: "Indoor walking",
        17: "Chair yoga",  # 坐姿专属
    }
    workout_type_reverse_mapping: Dict[str, int] = {
        v: k for k, v in workout_type_mapping.items()
    }

    # 姿势映射
    position_mapping: Dict[int, str] = {
        10: "Standing",
        11: "Seated on chair",
        12: "All position",
    }
    position_reverse_mapping: Dict[str, int] = {v: k for k, v in position_mapping.items()}

    # 限制部位映射
    restriction_mapping: Dict[int, str] = {
        10: "Shoulder",
        11: "Back",
        12: "Wrist",
        13: "Knee",
        14: "Ankle",
        15: "Hip",
    }
    restriction_reverse_mapping: Dict[str, int] = {
        v: k for k, v in restriction_mapping.items()
    }

    workout_mapping: Dict[str, Dict[int, str]] = {
        "exerciseTypeCodeSet": workout_type_mapping,
        "restrictionCodeSet": restriction_mapping,
        "durationCode": duration_mapping,
        "equipmentCodeSet": equipment_mapping,
        "coachGenderCode": coach_gender_mapping,
        "positionCode": position_mapping,
    }
    workout_reverse_mapping: Dict[str, Dict[str, int]] = {
        "exerciseTypeCodeSet": workout_type_reverse_mapping,
        "restrictionCodeSet": restriction_reverse_mapping,
        "durationCode": duration_reverse_mapping,
        "equipmentCodeSet": equipment_reverse_mapping,
        "coachGenderCode": coach_gender_reverse_mapping,
        "positionCode": position_reverse_mapping,
    }

    # key的映射
    key_mapping: Dict[str, str] = {
        "workout_type": "exerciseTypeCodeSet",
        "equipment": "equipmentCodeSet",
        "physical_limitation": "restrictionCodeSet",
        "workout_duration": "durationCode",
        "coach_gender": "coachGenderCode",
        "preferred_position": "positionCode",
    }
    key_reverse_mapping: Dict[str, str] = {v: k for k, v in key_mapping.items()}


class MappingsManager:
    @classmethod
    def get_mappings(cls, version: APIVersion) -> V1Mappings | V2Mappings:
        if version == APIVersion.V1:
            mappings = V1Mappings()
        elif version == APIVersion.V2:
            mappings = V2Mappings()
        else:
            raise VersionError(f"Version {version} not supported")
        return mappings
