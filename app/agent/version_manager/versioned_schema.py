from typing import List

from pydantic import BaseModel, Field

from app.agent.version_manager.version_manager import APIVersion, VersionError


class V1AdjustPlaneDes(BaseModel):
    workout_type: List[str] = (
        '用户需要修改运动类型，多选，可以增加也可以删除，可选值: ["Chair yoga", "Chair cardio", "Gentle cardio", "Indoor walking", "Tai chi", "Dancing", "All workout type"]，默认为: []'
    )
    physical_limitation: List[str] = (
        """用户需要避免锻炼的部位、用户感觉不舒服的部位或者用户不想锻炼的部位，可选值: ["Shoulder", "Back", "Wrist", "Hip", "Knee", "Ankle", "I'm all good"]，默认值: []"""
    )
    equipment: List[str] = (
        '用户想要修改的运动器械，可选值: ["Dumbbell", "Resistance Band", "No Equipment"]，默认为: []'
    )
    workout_duration: str = (
        '用户想要修改的运动时长，提供值为时间区间，单位为`分钟`，可选值: ["5-10 minutes", "10-15 minutes", "15-20 minutes", "20-30 minutes"]，默认为: ""'
    )
    coach_gender: str = (
        '用户想要修改的教练性别，可选值: ["Female", "Male", "Mixed Coaches"]，默认为: ""'
    )
    preferred_position: str = (
        '用户想要修改的运动姿势，可选值: ["Standing", "Seated on chair", "All position"]，默认为: ""'
    )


class V2AdjustPlaneDes(BaseModel):
    version: str = Field(default="v2", description="接口版本")

    workout_type: List[str] = (
        '用户需要修改运动类型，多选，可以增加也可以删除，可选值: ["Chair yoga", "Chair cardio", "Gentle cardio", "Indoor walking", "Tai chi", "Dancing", "All workout type"]，默认为: []'
    )
    physical_limitation: List[str] = (
        """用户需要避免锻炼的部位、用户感觉不舒服的部位或者用户不想锻炼的部位，可选值: ["Shoulder", "Back", "Wrist", "Hip", "Knee", "Ankle", "I'm all good"]，默认值: []"""
    )
    equipment: List[str] = (
        '用户想要修改的运动器械，可选值: ["2-6 lbs Dumbbell", "6-10 lbs Dumbbell", "Resistance Band", "No Equipment"]，默认为: []'
    )
    workout_duration: str = (
        '用户想要修改的运动时长，提供值为时间区间，单位为`分钟`，可选值: ["5-10 minutes", "10-15 minutes", "15-20 minutes", "20-30 minutes"]，默认为: ""'
    )
    coach_gender: str = (
        '用户想要修改的教练性别，可选值: ["Female", "Male", "Mixed Coaches"]，默认为: ""'
    )
    preferred_position: str = (
        '用户想要修改的运动姿势，可选值: ["Standing", "Seated on chair", "All position"]，默认为: ""'
    )


class LinkMapDes(BaseModel):
    contact_email_code: int = 0  # app内发送邮件
    check_up: int = 1  # 跳转苹果订阅管理
    restore_sub: int = 2  # app内恢复订阅
    buy_sub: int = 3  # app内购买订阅
    check_plan: int = 4  # 跳转当天plan详情页
    adjust_weight: int = 5  # 跳转体重页面
    step_data: int = 6  # 修改目标步数
    adjust_language: int = 7  # 修改语言
    check_favorite: int = 8  # 跳转收藏
    check_recent: int = 9  # 跳转历史记录(recent)页面
    search_workout: int = 10  # 跳转workouts页面（搜索workouts）
    login: int = 11  # 跳转登陆页
    signup: int = 12  # 跳转注册页
    adjust_name: int = 13  # 跳转姓名修改页
    adjust_age: int = 14  # 跳转年龄修改页面
    adjust_height: int = 15  # 跳转身高修改页面
    adjust_chronic: int = 16  # 跳转修改chronic页面
    adjust_goal: int = 17  # 跳转修改fitness goal页面
    refund_apple_support: str = "https://getsupport.apple.com"
    cancel_sub_support: str = "https://support.apple.com/HT202039"


class SchemaManager:
    @classmethod
    def get_schema(cls, version: APIVersion):
        if version == APIVersion.V1:
            tool_des = V1AdjustPlaneDes()
        elif version == APIVersion.V2:
            tool_des = V2AdjustPlaneDes()
        else:
            raise VersionError(f"Version {version} not supported")
        return tool_des
