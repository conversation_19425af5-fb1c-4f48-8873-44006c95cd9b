from typing import Dict, Any, Optional

from llama_index.core.workflow import Context
from llama_index.core.agent.workflow import <PERSON><PERSON>ut<PERSON>, <PERSON>lCall, ToolCallResult
from llama_index.core.llms import ChatMessage

from app.logger import get_logger
from app.agent.agent_saver import AgentSaver
from app.agent.version_manager.version_manager import APIVersion
from app.agent.agent_init import AgentInit
from app.api.v1.schema import WorkoutPlanModel
from app.api.v2.schema import WorkoutPlanV2

logger = get_logger("agent_chat")


async def multi_agent_chat(
        chat_id: str,
        user_query: str,
        user_language: str,
        user_workout_plan: WorkoutPlanModel | WorkoutPlanV2,
        sub_id: str,
        source: str,
        api_version: Optional[APIVersion] = None,
) -> Dict[str, Any]:
    """
    非流式聊天函数，返回完整的聊天响应
    使用LlamaIndex的人机交互形式管理对话状态
    """
    logger.info(
        f"开始非流式对话 chat_id={chat_id}, query={user_query}, user_language={user_language}"
    )

    # 根据用户语言和API版本，初始化Agent
    agent_client = AgentInit(version=api_version, user_language=user_language)
    agent_workflow = agent_client.init_agent()

    logger.info(f"使用API版本: {api_version}, 用户语言: {user_language}")

    # 初始化Agent Saver
    agent_saver = AgentSaver(chat_id=chat_id)
    await agent_saver.get_redis_client()

    # 初始化Memory并加载历史对话
    memory = await agent_saver.init_memory()
    logger.info(f"完成对话: {chat_id} 的Memory初始化")

    # 初始化Context，从redis中获取
    ctx_dict = await agent_saver.get_user_context()

    # 创建新的Context或从现有数据恢复
    if not ctx_dict:
        # 如果没有现有Context，创建新的
        ctx = Context(agent_workflow)
        logger.info(f"为对话 {chat_id} 创建新的Context")
    else:
        # 从现有数据恢复Context
        ctx = Context.from_dict(workflow=agent_workflow, data=ctx_dict)
        logger.info(f"为对话 {chat_id} 恢复 Context")

    # 处理 user_workout_plan
    user_workout_plan = user_workout_plan.model_dump()
    workout_type = user_workout_plan["exerciseTypeCodeSet"]

    # 先把15/21（哑铃）和16（弹力带）从workout type中删除
    if 15 in workout_type:
        logger.debug("清空workout type中的lightweight dumbbell参数")
        workout_type.remove(15)
    if 16 in workout_type:
        logger.debug("清空workout type中的弹力带参数")
        workout_type.remove(16)
    if 21 in workout_type:
        logger.debug("清空workout type中的 midweight dumbbell参数")
        workout_type.remove(21)
    user_workout_plan["exerciseTypeCodeSet"] = workout_type
    user_workout_plan = agent_client.tools.mapping_int_to_str(user_workout_plan)

    # 设置基本Context信息
    await ctx.store.set("chat_id", chat_id)
    await ctx.store.set("workout_mapping_data", user_workout_plan)
    logger.info(f"完成对话: {chat_id} 的Context初始化，API版本: {api_version}")

    ctx_memory = await ctx.store.get("memory", None)
    if not ctx_memory:
        await ctx.store.set("memory", memory)

    # 运行Agent工作流 - 非流式处理
    response_content = ""
    try:
        handler = agent_workflow.run(
            user_msg=ChatMessage(content=user_query),
            ctx=ctx,
            verbose=True,
            memory=memory,
        )

        # 处理事件流
        async for event in handler.stream_events():
            if isinstance(event, AgentOutput):
                logger.info(f"AgentOutput: {event.current_agent_name}")
                logger.debug(f"AgentOutput: {event.response.content}")
                if event.response.role == "assistant":
                    response_content = event.response.content

            if isinstance(event, ToolCall):
                logger.debug(f"ToolCall: {event.tool_name}")
                logger.debug(f"ToolCall: {event.tool_kwargs}")
                # 标记工具被调用

            if isinstance(event, ToolCallResult):
                logger.debug(f"ToolCallResult: {event.tool_name}")
                logger.debug(f"ToolCallResult: {event.tool_output}")

        logger.info(f"Response content: {response_content}")

        # 获取响应数据
        update_workout_data = await ctx.store.get("update_workout_data", {})
        refresh_workout_plan = await ctx.store.get("refresh_workout_plan", False)
        workout_updated_mapping_data = await ctx.store.get(
            "workout_updated_mapping_data", {}
        )

        update_workout_message = await ctx.store.get("update_workout_message", "")
        update_workout_code = await ctx.store.get("update_workout_code", 200)

        # 保存Memory
        await agent_saver.save_memory_to_redis(
            memory=memory, sub_id=sub_id, source=source
        )
        logger.info(f"成功更新 {chat_id} 的对话历史")

        # 保存Context到Redis
        await agent_saver.set_user_context(context_dict=ctx.to_dict(), chat_id=chat_id)
        logger.info(f"成功更新 {chat_id} 的Context")

        return {
            "data": {
                "response": response_content,
                "updated_workout_plan_data": update_workout_data,
                "refresh_workout_plan": refresh_workout_plan,
                "updated_workout_plan_map": workout_updated_mapping_data,
            },
            "code": update_workout_code,
            "message": update_workout_message,
        }

    except Exception as e:
        logger.error(f"Agent执行出错: {str(e)}")
        return {
            "data": {
                "response": "Sorry, an error occurred while processing your request.",
                "refresh_workout_plan": False,
            },
            "code": 500,
            "message": "failed",
        }
    finally:
        # 关闭redis链接
        await agent_saver.close_redis_connection()
        logger.info("关闭Redis连接")
