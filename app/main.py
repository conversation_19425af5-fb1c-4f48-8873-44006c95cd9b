import asyncio
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from slowapi.errors import RateLimitExceeded

from app.api.v1.routes import router as api_router, limiter, custom_rate_limit_handler as v1_handler
from app.api.v1 import routes as v1_routes
from app.api.v2 import routes as v2_routes
from app.api.v2.routes import custom_rate_limit_handler_v2 as v2_handler
from app.services.schedule_service import SimpleScheduledService
from app.logger import get_logger
from app.config import settings

logger = get_logger("main")

# 全局简化定时服务实例
simple_scheduled_service = SimpleScheduledService(interval_minutes=settings.DATA_FETCH_INTERVAL_MINUTES)


def is_main_worker() -> bool:
    """检查是否为主worker进程 - 使用文件锁确保只有一个主进程"""
    current_pid = os.getpid()
    lock_file = "/tmp/ai-coach-main-worker.lock"

    try:
        import fcntl

        # 尝试创建并锁定文件
        try:
            # 以写模式打开文件，如果不存在则创建
            lock_fd = os.open(lock_file, os.O_CREAT | os.O_WRONLY | os.O_TRUNC, 0o644)

            # 尝试获取非阻塞排他锁
            fcntl.flock(lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)

            # 成功获取锁，写入当前PID
            os.write(lock_fd, str(current_pid).encode())
            os.fsync(lock_fd)

            logger.info(f"🎯 进程 {current_pid} 成功获取主进程锁")

            # 不关闭文件描述符，保持锁定状态
            # 进程结束时会自动释放锁
            return True

        except (OSError, IOError) as e:
            # 无法获取锁，说明已有其他进程是主进程
            try:
                # 读取锁文件中的主进程PID
                with open(lock_file, 'r') as f:
                    main_pid = f.read().strip()
                logger.info(f"🔍 进程 {current_pid} 未获取到锁，主进程是 PID {main_pid}")
            except:
                logger.info(f"🔍 进程 {current_pid} 未获取到锁，作为子进程运行")
            return False

    except ImportError:
        # 如果没有fcntl模块，使用简单的文件存在检查
        try:
            if not os.path.exists(lock_file):
                # 文件不存在，创建文件并写入PID
                with open(lock_file, 'w') as f:
                    f.write(str(current_pid))
                logger.info(f"🎯 进程 {current_pid} 创建锁文件，设为主进程")
                return True
            else:
                # 文件已存在，读取主进程PID
                try:
                    with open(lock_file, 'r') as f:
                        main_pid = f.read().strip()
                    logger.info(f"🔍 进程 {current_pid} 检测到主进程 PID {main_pid}")
                    return current_pid == int(main_pid)
                except:
                    logger.warning(f"⚠️ 进程 {current_pid} 无法读取锁文件，默认为子进程")
                    return False
        except Exception as e:
            logger.warning(f"⚠️ 文件锁操作失败: {e}，默认为主进程")
            return True


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理器 - 只在主进程启动定时任务"""
    worker_id = os.getenv("UVICORN_WORKER_ID", "main")
    current_pid = os.getpid()
    logger.info(f"AI Coach应用启动中... (Worker ID: {worker_id}, PID: {current_pid})")

    # 检查是否为主进程
    data_task = None
    is_main = is_main_worker()

    try:
        if is_main:
            # 只在主进程中启动定时任务
            data_task = asyncio.create_task(
                simple_scheduled_service.start_scheduled_task()
            )
            logger.info(f"🕐 主进程 {current_pid} 启动定时数据获取服务")
        else:
            logger.info(f"⚡ 子进程 {current_pid} 启动完成（不启动定时任务）")

        yield

    finally:
        # 应用关闭时停止定时数据服务
        logger.info(f"AI Coach应用关闭中... (Worker ID: {worker_id}, PID: {current_pid})")

        if is_main and data_task:
            if not data_task.done():
                data_task.cancel()
                try:
                    await data_task
                except asyncio.CancelledError:
                    logger.info("定时数据任务已取消")

            await simple_scheduled_service.stop_scheduled_task()
            logger.info("定时数据服务已停止")
        else:
            logger.info(f"Worker {worker_id} (PID: {current_pid}) 关闭完成")


app = FastAPI(
    title="AI Coach Agent API",
    description="基于llamaindex构建的多Agent AI Coach",
    version="1.0.0",
    lifespan=lifespan
)

# 添加 slowapi 状态管理
app.state.limiter = limiter

# 创建统一的限速异常处理器（根据路径自动选择版本）
async def unified_rate_limit_handler(request: Request, exc: RateLimitExceeded):
    """统一的限速异常处理器 - 根据请求路径自动选择合适的版本处理器"""
    path = request.url.path

    # 根据路径前缀选择对应的处理器
    if path.startswith("/v2/api"):
        return await v2_handler(request, exc)
    elif path.startswith("/v1/api"):
        return await v1_handler(request, exc)
    else:
        # 默认使用V1处理器（兼容性）
        return await v1_handler(request, exc)

# 注册统一的限速异常处理器
app.add_exception_handler(RateLimitExceeded, unified_rate_limit_handler)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含版本化路由系统
app.include_router(v1_routes.router, prefix="/v1/api", tags=["v1"])
app.include_router(v2_routes.router, prefix="/v2/api", tags=["v2"])
