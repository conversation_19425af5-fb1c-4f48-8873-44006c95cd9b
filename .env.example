# LLM配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4.1-mini-2025-04-14

# 超时配置
# Agent超时配置，单位：秒
AGENT_TIMEOUT=30
# API连接超时配置，单位：秒
API_TIMEOUT=30

#Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_TTL=1800
REDIS_CONTEXT_NAME=COACH-SARAH-CONTEXT
KEY_TITLE=COACH-SARAH

# Tool的API配置
URL_MAP='{
    "v1": "https://core-app-test-large.7mfitness.com/cmsApp/OOG116/workoutGenerate/v5/plan",
    "v2": "https://core-app-test-large.7mfitness.com/cmsApp/OOG116/workoutGenerate/v5/plan"
}'

# 飞书配置
APP_ID=cli_your_app_id_here
APP_SECRET=your_app_secret_here
# 收集对话历史的飞书多维表格参数
APP_TOKEN=your_app_token_here
FALLBACK_RECORD_COUNT=1000
# 定时采集的数据时间差(分钟)
SYNC_TIME=30

# ENV
# 本地环境：Local
# AWS环境：AWS
ENV=Local
DEBUG=False

# 定时任务配置，单位分钟
DATA_FETCH_INTERVAL_MINUTES=10
DATA_RETENTION_HOURS=24

# Uvicorn服务器配置
UVICORN_WORKERS=4
UVICORN_HOST=0.0.0.0
UVICORN_PORT=20000
UVICORN_LOG_LEVEL=info

# Slack Webhook，用于发送超限告警
SLACK_WEBHOOK=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK