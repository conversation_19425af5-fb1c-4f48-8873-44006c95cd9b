services:
  ai-coach:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-coach-app
    ports:
      - "${UVICORN_PORT:-8000}:${UVICORN_PORT:-8000}"
    env_file:
      - .env
    
    volumes:
      # 挂载日志目录到宿主机
      - ./logs:/app/logs
      # 挂载代码目录实现热更新（开发模式）
      # 如果不需要代码挂载，可以注释掉下面这行
      - ./app:/app/app  # 移除ro限制，允许日志写入

    networks:
      - ai-coach-network
      - default  # 加入默认网络以连接到 Redis
    # 重启策略
    restart: unless-stopped

networks:
  ai-coach-network:
    driver: bridge
